export enum PaymentMethodEnum {
  electronic = 'transfer',
  cash = 'cash',
}

export enum SUBSCRIPTION_STATUS {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  PENDING = 'pending',
  FAILED = 'failed',
  DOWNGRADED = 'downgraded',
  UPGRADED = 'upgraded',
  CANCELLED = 'cancelled',
}
export enum PLAN_NAME {
  FREE = 'Free Pass',
  DAILY = 'Day Pass',
  WEEKLY = 'Week Pass',
  MONTHLY = 'Month Pass',
  ANNUAL = 'Annual Pass',
}
export enum BILLING_CYCLE {
  FREE = 'FREE',
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  ANNUAL = 'ANNUAL',
}
export enum PRICING {
  FREE = 0.0,
  DAILY = 2.99,
  WEEKLY = 4.99,
  MONTHLY = 9.99,
  ANNUAL = 99,
}
export enum PRICING_TYPE {
  FLAT = 'flat',
  TIERED = 'tiered',
  USAGE_BASED = 'usage-based',
}

export const PLAN_FEATURES = {
  FREE: {
    authorized: [
      '2 free payslip generation monthly',
      'Customize your payslip',
      'Payslip analytics dashboard',
      'Payslip historical records',
      'No access to Digit-tally suite',
    ],
    restricted: ['No access to Digit-tally suite'],
  },
  DAILY: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '1 day access',
      'Unlimited payslip generation',
    ],
    restricted: ['No access to Digit-tally suite'],
  },
  WEEKLY: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '7 days access to payslip',
      'Unlimited payslip generation',
    ],
    restricted: ['No access to Digit-tally suite'],
  },
  MONTHLY: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '30 days access to payslip',
      'Unlimited payslip generation',
    ],
    restricted: ['No access to Digit-tally suite'],
  },
  ANNUAL: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '1 year access to payslip',
      'Unlimited payslip generation',
    ],
    restricted: ['No access to Digit-tally suite'],
  },
};

export const UPDATED_PLAN_FEATURES = {
  FREE: {
    authorized: [
      '2 free payslip generation monthly',
      'Customize your payslip',
      'Payslip analytics dashboard',
      'Payslip historical records',
    ],
    restricted: [],
  },
  DAILY: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '1 day access',
      'Unlimited payslip generation',
    ],
    restricted: [],
  },
  WEEKLY: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '7 days access to payslip',
      'Unlimited payslip generation',
    ],
    restricted: [],
  },
  MONTHLY: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '30 days access to payslip',
      'Unlimited payslip generation',
    ],
    restricted: [],
  },
  ANNUAL: {
    authorized: [
      'Everything in free pass',
      'One-time payment',
      '1 year access to payslip',
      'Unlimited payslip generation',
    ],
    restricted: [],
  },
};

export const PLAN_NAME_ARRAY = Object.values(PLAN_NAME);
export const PRICING_ARRAY = Object.values(PRICING);
export const BILLING_CYCLE_ARRAY = Object.values(BILLING_CYCLE);
export const PRICING_TYPE_ARRAY = Object.values(PRICING_TYPE);
export const SUBSCRIPTION_STATUS_ARRAY = Object.values(SUBSCRIPTION_STATUS);
