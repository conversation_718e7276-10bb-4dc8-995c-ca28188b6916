import { NextFunction, Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import logger from '../../utilities/logger';
import { AppError, BadRequestError, NotPermittedError } from '../error_handlers/app-error';
import {
  calcEntityAmountAndGetAmounts,
  calcSubTotalAmount,
  calcTotalAmount,
  convertHexToRgba,
  // currentTimestamp,
  formatAllEntities,
  formatDateToDayMonthYear,
  // formatDateToDayMonthYear,
  formatNumberWithCommas,
  trimAndLowerCase,
} from '../../utilities/global.utilities';
import {
  DOCUMENT_ENTITY_TYPE,
  DOCUMENT_ENTITY_TYPE_ARRAY,
  DOCUMENT_STATUS,
  DOCUMENT_TYPE,
} from '../../constants/values.constants';
import {
  contentSchema,
  customerSchema,
  decoupleEntities,
  invoiceSchema,
  itemSchema,
  serviceSchema,
  updateCRNitemSchema,
  updateCRNserviceSchema,
} from './helpers.utils';
import {
  isAllowedDocument,
  isAllowedDocumentEntity,
  isSuperAdmin,
  isValuePresent,
} from '../../utilities/guards';
import { Errors } from '../../constants/errors.constants';
import { getJoiValidationErrorMessage } from '../../helpers/error.helpers';
import { catchAsync } from '../../utilities/catch-async-error';

export const validateSystemCreateInvoice = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object()
      .required()
      .keys({
        customer: customerSchema.required().messages({
          'any.required': 'Customer is required',
        }),
        // invoiceSchema,
        orgId: Joi.string()
          .regex(/^(dgt-)[0-9a-zA-Z]+$/)
          .required()
          .messages({
            'string.base': 'orgId must be a string.',
            'string.pattern.base': 'orgId must be a valid organization ID.',
            'any.required': 'orgId is required',
          }),
        notes: Joi.string().max(250).allow('').messages({
          'string.base': 'notes must be a string.',
          'string.max': 'notes must be maximum of 250 characters',
        }),

        dateIssued: Joi.date().optional().allow(''),
        draft: Joi.boolean().default(false).messages({
          'boolean.base': 'draft must be a boolean value',
        }),
        entityType: Joi.string()
          .valid(DOCUMENT_ENTITY_TYPE.PRODUCT, DOCUMENT_ENTITY_TYPE.SERVICE)
          .required()
          .messages({
            'any.required': 'Entity type is required.',
            'any.only':
              'Invalid entity type. Valid values are: ' + DOCUMENT_ENTITY_TYPE_ARRAY.join(', '),
            'string.base': 'Entity type must be a string.',
          }),
        items: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.PRODUCT,
          then: Joi.array().items(itemSchema).min(1).required().messages({
            'array.base': 'Items must be an array.',
            'array.min': 'At least one item is required.',
            'any.required': 'Items are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
        services: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.SERVICE,
          then: Joi.array().items(serviceSchema).min(1).required().messages({
            'array.base': 'Services must be an array.',
            'array.min': 'At least one service is required.',
            'any.required': 'Services are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
      })
      // .append(invoiceSchema)
      .xor('items', 'services')
      .messages({
        'object.missing': 'you must provide either items or services in the entities.',
        'object.xor': 'either items or services can be provided and not both.',
      });

    const requestBody = req.body;
    requestBody.entityType = trimAndLowerCase(requestBody.entityType);
    //  decoupleEntities(requestBody);
    if (typeof requestBody.items === 'string' && requestBody.items) {
      try {
        requestBody.items = JSON.parse(requestBody.items);
      } catch (error) {
        console.error('Error parsing items string:', error);
      }
    }
    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    // const timestamp = currentTimestamp(req);
    // populateCreateEntitiesTimestamp(requestBody, timestamp);
    const { customer, orgId, paid, terms, ...invoice } = req.body;
    const invoiceDTO = {
      ...invoice,
      orgId,
      type: DOCUMENT_TYPE.INVOICE,
      dateIssued: invoice.dateIssued ?? null,
      dueDate: invoice.dateIssued ?? null,
      status: invoice.draft ? DOCUMENT_STATUS.DRAFT : DOCUMENT_STATUS.AWAITING_PAYMENT,
    };
    req.body = { customer, orgId, paid, terms, ...invoiceDTO };

    if (!isAllowedDocument(req.body.type)) {
      throw new BadRequestError(Errors.INVALID_DOCUMENT_TYPE);
    }

    next();
  }
);

export const validateSystemCreateReceipt = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object()
      .required()
      .keys({
        datePaid: Joi.date().optional().allow(''),
        expiryDate: Joi.date().optional().allow(''),
        amountPaid: Joi.number().min(0.0).precision(2).default(0.0).messages({
          'number.base': 'Amount paid must be a number',
          'number.min': 'Amount paid must be minimum of 0.0',
          'number.precision': 'Amount paid must be in two decimal places',
        }),
        reference: Joi.string().required().messages({
          'string.base': 'reference must be a string.',
          'string.required': 'reference is required',
        }),
        currency: Joi.string().max(3).min(1).required().messages({
          'string.base': 'currency must be a string.',
          'string.min': 'currency must be minimum character of 1.',
          'string.max': 'currency must be maximum character of 3.',
          'string.required': 'currency is required',
        }),
        invoiceId: Joi.string().required().messages({
          'string.base': 'invoiceId must be a string.',
          'string.required': 'invoiceId is required',
        }),
        orgId: Joi.string()
          .regex(/^(dgt-)[0-9a-zA-Z]+$/)
          .required()
          .messages({
            'string.base': 'orgId must be a string.',
            'string.pattern.base': 'orgId must be a valid organization ID.',
            'any.required': 'orgId is required',
          }),
        paymentMethod: Joi.string().required().messages({
          'string.base': 'paymentMethod must be a string.',
          'string.required': 'paymentMethod is required',
        }),
      });
    const requestBody = req.body;

    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    // const timestamp = currentTimestamp(req);
    // populateCreateEntitiesTimestamp(requestBody, timestamp);
    const { customer, orgId, paid, terms, ...invoice } = req.body;
    const invoiceDTO = {
      ...invoice,
      orgId,
      type: DOCUMENT_TYPE.INVOICE,
      dateIssued: invoice.dateIssued ?? null,
      dueDate: invoice.dateIssued ?? null,
      status: invoice.draft ? DOCUMENT_STATUS.DRAFT : DOCUMENT_STATUS.AWAITING_PAYMENT,
    };
    req.body = { customer, orgId, paid, terms, ...invoiceDTO };

    if (!isAllowedDocument(req.body.type)) {
      throw new BadRequestError(Errors.INVALID_DOCUMENT_TYPE);
    }

    next();
  }
);

export const validateCreateInvoice = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema: Joi.ObjectSchema = Joi.object()
      .required()
      .keys({
        customerId: Joi.string()
          .guid({ version: ['uuidv4'] })
          .required()
          .messages({
            'string.guid': 'customerId must be a valid UUID',
            'string.base': 'customerId must be a string',
            'string.empty': 'customerId cannot be empty',
            'any.required': 'customerId is required',
          }),
        // ...invoiceSchema,
      })
      .concat(invoiceSchema);

    const requestBody = decoupleEntities(req.body);

    if (typeof requestBody.items === 'string' && requestBody.items) {
      try {
        requestBody.items = JSON.parse(requestBody.items);
      } catch (error) {
        console.error('Error parsing items string:', error);
      }
    }

    requestBody.entityType = trimAndLowerCase(requestBody.entityType);
    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    if (!isSuperAdmin(req.user.role) && isValuePresent(req.body.orgId)) {
      throw new NotPermittedError(Errors.NOT_PERMITTED);
    }

    if (isSuperAdmin(req.user.role) && !isValuePresent(req.body.orgId)) {
      requestBody.orgId = req.user.organization.id;
    }

    if (!isSuperAdmin(req.user.role)) {
      requestBody.orgId = req.user.organization.id;
    }

    const { orgId } = res.locals;
    // const timestamp = currentTimestamp(req);
    req.body.orgId = orgId;
    // req.body.createdAt = timestamp;
    // req.body.updatedAt = timestamp;
    // populateCreateEntitiesTimestamp(requestBody, timestamp);
    next();
  }
);

export const validateCreateReceipt = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema: Joi.ObjectSchema = Joi.object()
      .required()
      .keys({
        invoiceId: Joi.string()
          .guid({ version: ['uuidv4'] })
          .required()
          .messages({
            'string.base': 'invoiceId must be a string',
            'string.guid': 'invoiceId must be a valid UUID',
            'string.empty': 'invoiceId cannot be empty',
            'any.required': 'invoiceId is required',
          }),
        amountPaid: Joi.number().required().messages({
          'number.base': 'Amount paid must be a number',
          'any.required': 'Amount paid is required',
        }),
      });
    const requestBody = req.body;
    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.details[0].message.replace(/"+/g, ''));
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }
    if (!isSuperAdmin(req.user.role) && isValuePresent(req.body.orgId))
      throw new NotPermittedError(Errors.NOT_PERMITTED);

    if (isSuperAdmin(req.user.role) && !isValuePresent(req.body.orgId)) {
      requestBody.orgId = req.user.organization.id;
    }
    if (!isSuperAdmin(req.user.role)) {
      requestBody.orgId = req.user.organization.id;
    }
    // const timestamp = currentTimestamp(req);
    // req.body.createdAt = timestamp;
    // req.body.updatedAt = timestamp;
    next();
  }
);

export const validateCreateCreditNote = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema: Joi.ObjectSchema = Joi.object()
      .required()
      .keys({
        invoiceId: Joi.string()
          .guid({ version: ['uuidv4'] })
          .required()
          .messages({
            'string.base': 'invoiceId must be a string',
            'string.guid': 'invoiceId must be a valid UUID',
            'string.empty': 'invoiceId cannot be empty',
            'any.required': 'invoiceId is required',
          }),
        notes: Joi.string().max(250).allow('').messages({
          'string.base': 'notes must be a string.',
          'string.max': 'notes must be maximum of 250 characters',
        }),
        draft: Joi.boolean().default(false).required().messages({
          'boolean.base': 'draft must be a boolean value',
          'any.required': 'draft is required',
        }),
        dateIssued: Joi.date().optional().allow('').allow(''),
        entityType: Joi.string()
          .valid(DOCUMENT_ENTITY_TYPE.PRODUCT, DOCUMENT_ENTITY_TYPE.SERVICE)
          .required()
          .messages({
            'any.required': 'Entity type is required.',
            'any.only':
              'Invalid entity type. Valid values are: ' + DOCUMENT_ENTITY_TYPE_ARRAY.join(', '),
            'string.base': 'Entity type must be a string.',
          }),
        items: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.PRODUCT,
          then: Joi.array().items(updateCRNitemSchema).min(1).required().messages({
            'array.base': 'Items must be an array.',
            'array.min': 'At least one item is required.',
            'any.required': 'Items are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
        services: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.SERVICE,
          then: Joi.array().items(updateCRNserviceSchema).min(1).required().messages({
            'array.base': 'Services must be an array.',
            'array.min': 'At least one service is required.',
            'any.required': 'Services are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
      })
      .xor('items', 'services')
      .messages({
        'object.missing': 'you must provide either items or services in the entities.',
        'object.xor': 'either items or services can be provided and not both.',
      });
    const requestBody = decoupleEntities(req.body);

    if (typeof requestBody.items === 'string' && requestBody.items) {
      try {
        requestBody.items = JSON.parse(requestBody.items);
      } catch (error) {
        console.error('Error parsing items string:', error);
      }
    }
    requestBody.entityType = trimAndLowerCase(requestBody.entityType);
    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }
    if (!isSuperAdmin(req.user.role) && isValuePresent(req.body.orgId)) {
      throw new NotPermittedError(Errors.NOT_PERMITTED);
    }
    if (isSuperAdmin(req.user.role) && !isValuePresent(req.body.orgId)) {
      requestBody.orgId = req.user.organization.id;
    }
    if (!isSuperAdmin(req.user.role)) {
      requestBody.orgId = req.user.organization.id;
    }
    // const timestamp = currentTimestamp(req);
    // req.body.orgId = orgId;
    // req.body.createdAt = timestamp;
    // req.body.updatedAt = timestamp;
    // populateCreateEntitiesTimestamp(requestBody, timestamp);
    next();
  }
);

export const validateSendDocument = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema: Joi.ObjectSchema = Joi.object()
      .required()
      .keys({
        email: Joi.string().email({ minDomainSegments: 2 }).required().messages({
          'string.base': 'Email must be a string',
          'string.email': 'Email must be a valid email address',
          'any.required': 'Please enter an email address',
        }),
        content: contentSchema.required().messages({
          'any.required': 'Content is required.',
        }),
      });

    const data = JSON.parse(JSON.stringify(req.body));
    const requestBody = decoupleEntities(JSON.parse(JSON.stringify(req.body)).content);

    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    if (!isAllowedDocument(req.body.content.documentType)) {
      throw new BadRequestError(Errors.INVALID_DOCUMENT_TYPE);
    }
    if (!isAllowedDocumentEntity(req.body.content.entityType)) {
      throw new BadRequestError(Errors.INVALID_ENTITY);
    }

    const { documentType, entityType } = req.body.content;

    req.body.content.documentType = trimAndLowerCase(documentType);
    req.body.content.entityType = trimAndLowerCase(entityType);
    data.content.bgColorOpacity = convertHexToRgba(data.content.bgColor, 0.1);
    const isServiceEntity = req.body.content.entityType === 'service';

    const { entityData, amounts, totalFees, totalVat, totalDiscount } =
      calcEntityAmountAndGetAmounts(data.content.entities, data.content.entityType);

    const subTotalAmount = calcSubTotalAmount(isServiceEntity ? totalFees : amounts);
    const totalAmount = calcTotalAmount(subTotalAmount, totalVat, totalDiscount);
    if (totalAmount === 0) throw new BadRequestError(Errors.AMOUNT_IS_ZERO);
    data.content.entities = formatAllEntities(entityData, req.body.content.entityType);
    data.content.totalVat = formatNumberWithCommas(totalVat);
    data.content.totalDiscount = formatNumberWithCommas(totalDiscount);
    data.content.totalAmount = formatNumberWithCommas(totalAmount);
    data.content.subTotalAmount = formatNumberWithCommas(subTotalAmount);
    data.content.amountPaid = formatNumberWithCommas(data.content.amountPaid);
    data.content.dateIssued = formatDateToDayMonthYear(data.content.dateIssued, req.userTimezone);
    data.content.dueDate = formatDateToDayMonthYear(data.content.dueDate, req.userTimezone);
    data.content.documentType =
      data.content.documentType.charAt(0).toUpperCase() + data.content.documentType.slice(1);
    req.body = data;

    next();
  }
);

// export const validateUpdateDocument = catchAsync(
//   async (req: Request, res: Response, next: NextFunction) => {
//     const schema: Joi.ObjectSchema = Joi.object()
//       .required()
//       .keys({
//         logo: Joi.string().allow(''),
//         customerId: Joi.string()
//           .guid({ version: ['uuidv4'] })
//           .optional()
//           .messages({
//             'string.guid': 'customerId must be a valid UUID',
//             'string.base': 'customerId must be a string',
//             'string.empty': 'customerId cannot be empty',
//           }),
//         businessName: Joi.string().allow(''),
//         businessAddress: Joi.string().allow(''),
//         businessCountry: Joi.string().allow(''),
//         notes: Joi.string().max(250).allow('').messages({
//           'string.base': 'notes must be a string.',
//           'string.max': 'notes must be maximum of 250 characters',
//         }),
//         invoiceId: Joi.string()
//           .guid({ version: ['uuidv4'] })
//           .required()
//           .messages({
//             'string.base': 'invoiceId must be a string',
//             'string.guid': 'invoiceId must be a valid UUID',
//             'string.empty': 'invoiceId cannot be empty',
//             'any.required': 'invoiceId is required',
//           }),
//         dateIssued: Joi.date().optional().allow(''),
//         dueDate: Joi.date().optional().allow(''),
//         draft: Joi.boolean().default(false).required().messages({
//           'boolean.base': 'Draft must be a boolean value',
//           'any.required': 'draft is required',
//         }),
//         entityType: Joi.string()
//           .valid(DOCUMENT_ENTITY_TYPE.PRODUCT, DOCUMENT_ENTITY_TYPE.SERVICE)
//           .required()
//           .messages({
//             'any.required': 'Entity type is required.',
//             'any.only':
//               'Invalid entity type. Valid values are: ' + DOCUMENT_ENTITY_TYPE_ARRAY.join(', '),
//             'string.base': 'Entity type must be a string.',
//           }),
//         items: Joi.when('entityType', {
//           is: DOCUMENT_ENTITY_TYPE.PRODUCT,
//           then: Joi.array().items(itemSchema).min(1).required().messages({
//             'array.base': 'Items must be an array.',
//             'array.min': 'At least one item is required.',
//             'any.required': 'Items are required.',
//           }),
//           otherwise: Joi.forbidden(),
//         }),
//         services: Joi.when('entityType', {
//           is: DOCUMENT_ENTITY_TYPE.SERVICE,
//           then: Joi.array().items(serviceSchema).min(1).required().messages({
//             'array.base': 'Services must be an array.',
//             'array.min': 'At least one service is required.',
//             'any.required': 'Services are required.',
//           }),
//           otherwise: Joi.forbidden(),
//         }),
//       })
//       .xor('items', 'services')
//       .messages({
//         'object.missing': 'you must provide either items or services in the entities.',
//         'object.xor': 'either items or services can be provided and not both.',
//       });
//     const requestBody = decoupleEntities(req.body);

//     if (typeof requestBody.items === 'string' && requestBody.items) {
//       try {
//         requestBody.items = JSON.parse(requestBody.items);
//       } catch (error) {
//         console.error('Error parsing items string:', error);
//       }
//     }
//     const { error } = schema.validate(requestBody);
//     if (error) {
//       logger.error(error.message);
//       return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
//     }

//     req.body.status =
//       req.body.draft === true ? DOCUMENT_STATUS.DRAFT : DOCUMENT_STATUS.AWAITING_PAYMENT;
//     req.body.dateIssued = req.body.dateIssued || null;
//     req.body.dueDate = req.body.dueDate || null;
//     // req.body.updatedAt = currentTimestamp(req);
//     // populateUpdateEntitiesTimestamp(req, requestBody);
//     next();
//   }
// );

export const validateUpdateCreditNote = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema: Joi.ObjectSchema = Joi.object()
      .required()
      .keys({
        notes: Joi.string().max(250).allow('').messages({
          'string.base': 'notes must be a string.',
          'string.max': 'notes must be maximum of 250 characters',
        }),
        dueDate: Joi.date().optional().allow(''),
        draft: Joi.boolean().default(false).required().messages({
          'boolean.base': 'draft must be a boolean value',
          'any.required': 'draft is required',
        }),
        entityType: Joi.string()
          .valid(DOCUMENT_ENTITY_TYPE.PRODUCT, DOCUMENT_ENTITY_TYPE.SERVICE)
          .required()
          .messages({
            'any.required': 'Entity type is required.',
            'any.only':
              'Invalid entity type. Valid values are: ' + DOCUMENT_ENTITY_TYPE_ARRAY.join(', '),
            'string.base': 'Entity type must be a string.',
          }),
        items: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.PRODUCT,
          then: Joi.array().items(updateCRNitemSchema).min(1).required().messages({
            'array.base': 'Items must be an array.',
            'array.min': 'At least one item is required.',
            'any.required': 'Items are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
        services: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.SERVICE,
          then: Joi.array().items(updateCRNserviceSchema).min(1).required().messages({
            'array.base': 'Services must be an array.',
            'array.min': 'At least one service is required.',
            'any.required': 'Services are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
      })
      .xor('items', 'services')
      .messages({
        'object.missing': 'you must provide either items or services in the entities.',
        'object.xor': 'either items or services can be provided and not both.',
      });
    const requestBody = decoupleEntities(req.body);

    if (typeof requestBody.items === 'string' && requestBody.items) {
      try {
        requestBody.items = JSON.parse(requestBody.items);
      } catch (error) {
        console.error('Error parsing items string:', error);
      }
    }
    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    req.body.status =
      req.body.draft === true ? DOCUMENT_STATUS.DRAFT : DOCUMENT_STATUS.AWAITING_PAYMENT;
    req.body.dueDate = req.body.dueDate || null;
    // req.body.updatedAt = currentTimestamp(req);
    // populateUpdateEntitiesTimestamp(req, requestBody);
    next();
  }
);

export const validateUpdateInvoice = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object()
      .required()
      .keys({
        customerId: Joi.string()
          .guid({ version: ['uuidv4'] })
          .required()
          .messages({
            'string.guid': 'customerId must be a valid UUID',
            'string.empty': 'customerId cannot be empty',
            'string.base': 'customerId must be a string',
            'any.required': 'customerId is required',
          }),
        orgId: Joi.string()
          .regex(/^(dgt-)[0-9a-zA-Z]+$/)
          .optional()
          .messages({
            'string.base': 'orgId must be a string.',
            'string.pattern.base': 'orgId must be a valid organization ID.',
          }),
        notes: Joi.string().max(250).allow('').messages({
          'string.base': 'notes must be a string.',
          'string.max': 'notes must be maximum of 250 characters',
        }),
        dateIssued: Joi.date().optional().allow(''),
        dueDate: Joi.date().optional().allow(''),
        draft: Joi.boolean().default(false).required().messages({
          'boolean.base': 'Draft must be a boolean value',
          'any.required': 'draft is required',
        }),
        entityType: Joi.string()
          .valid(DOCUMENT_ENTITY_TYPE.PRODUCT, DOCUMENT_ENTITY_TYPE.SERVICE)
          .required()
          .messages({
            'any.required': 'Entity type is required.',
            'any.only':
              'Invalid entity type. Valid values are: ' + DOCUMENT_ENTITY_TYPE_ARRAY.join(', '),
            'string.base': 'Entity type must be a string.',
          }),
        items: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.PRODUCT,
          then: Joi.array().items(itemSchema).min(1).required().messages({
            'array.base': 'Items must be an array.',
            'array.min': 'At least one item is required.',
            'any.required': 'Items are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
        services: Joi.when('entityType', {
          is: DOCUMENT_ENTITY_TYPE.SERVICE,
          then: Joi.array().items(serviceSchema).min(1).required().messages({
            'array.base': 'Services must be an array.',
            'array.min': 'At least one service is required.',
            'any.required': 'Services are required.',
          }),
          otherwise: Joi.forbidden(),
        }),
      })
      .xor('items', 'services')
      .messages({
        'object.missing': 'you must provide either items or services in the entities.',
        'object.xor': 'either items or services can be provided and not both.',
      });

    const requestBody = decoupleEntities(req.body);

    if (typeof requestBody.items === 'string' && requestBody.items) {
      try {
        requestBody.items = JSON.parse(requestBody.items);
      } catch (error) {
        console.error('Error parsing items string:', error);
      }
    }
    const { error } = schema.validate(requestBody, { stripUnknown: true });
    if (error) throw new BadRequestError(getJoiValidationErrorMessage(error));

    req.body = requestBody;

    req.body.status = req.body.draft ? DOCUMENT_STATUS.DRAFT : DOCUMENT_STATUS.AWAITING_PAYMENT;
    req.body.dateIssued = req.body.dateIssued || null;
    req.body.dueDate = req.body.dueDate || null;
    // req.body.updatedAt = currentTimestamp(req);
    // populateUpdateEntitiesTimestamp(req, requestBody);
    next();
  }
);

export const validateDownloadDocument = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema = contentSchema;
    const data = JSON.parse(JSON.stringify(req.body));

    const requestBody = decoupleEntities(JSON.parse(JSON.stringify(req.body)));
    if (!requestBody) return;
    const { error } = schema.validate(requestBody);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    if (!isAllowedDocument(req.body.documentType)) {
      throw new BadRequestError(Errors.INVALID_DOCUMENT_TYPE);
    }

    if (!isAllowedDocumentEntity(req.body.entityType)) {
      throw new BadRequestError(Errors.INVALID_ENTITY);
    }

    const { documentType, entityType } = req.body;

    req.body.documentType = trimAndLowerCase(documentType);
    req.body.entityType = trimAndLowerCase(entityType);
    data.bgColorOpacity = convertHexToRgba(data.bgColor, 0.1);
    const isServiceEntity = req.body.entityType === 'service';

    const { entityData, amounts, totalFees, totalVat, totalDiscount } =
      calcEntityAmountAndGetAmounts(data.entities, data.entityType);

    const subTotalAmount = calcSubTotalAmount(isServiceEntity ? totalFees : amounts);
    const totalAmount = calcTotalAmount(subTotalAmount, totalVat, totalDiscount);

    if (totalAmount === 0) throw new BadRequestError(Errors.AMOUNT_IS_ZERO);

    data.entities = formatAllEntities(entityData, req.body.entityType);
    data.totalVat = formatNumberWithCommas(totalVat);
    data.totalDiscount = formatNumberWithCommas(totalDiscount);
    data.totalAmount = formatNumberWithCommas(totalAmount);
    data.subTotalAmount = formatNumberWithCommas(subTotalAmount);
    data.amountPaid = formatNumberWithCommas(data.amountPaid);
    data.dateIssued = formatDateToDayMonthYear(data.dateIssued, req.userTimezone);
    data.dateIssued = formatDateToDayMonthYear(data.dateIssued, req.userTimezone);
    data.dueDate = formatDateToDayMonthYear(data.dueDate, req.userTimezone);
    data.documentType = data.documentType.charAt(0).toUpperCase() + data.documentType.slice(1);
    req.body = data;
    next();
  }
);

export const validateDownloadDocumentById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object()
      .required()
      .keys({
        documentNumber: Joi.string().required().messages({
          'string.base': 'documentNumber must be a string.',
          'any.required': 'documentNumber is required as a query parameter.',
        }),
      });

    const { error } = schema.validate(req.query, { stripUnknown: true });
    if (error) {
      logger.error(error);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    // req.body = { ...value, orgId: req.user.organization.id };
    next();
  }
);

export const validateSendDocumentById = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object()
      .required()
      .keys({
        email: Joi.string().email({ minDomainSegments: 2 }).required().messages({
          'string.base': 'Your email must be a string',
          'string.email': 'Please enter a valid email address',
          'string.empty': 'Customer email address is missing',
          'any.required': 'Customer email address is missing',
        }),
        documentNumber: Joi.string().required().messages({
          'string.base': 'documentNumber must be a string.',
          'any.required': 'documentNumber is required',
        }),
        // orgId: Joi.string().required().messages({
        //   'string.base': 'orgId must be a string.',
        //   'any.required': 'orgId is required',
        // }),
      });

    const { error } = schema.validate(req.body, { abortEarly: true });
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }
    next();
  }
);

export const validateInvoiceReminder = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object({
      documentId: Joi.string()
        .guid({ version: ['uuidv4'] })
        .required()
        .messages({
          'string.guid': 'documentId must be a valid UUID',
          'string.base': 'documentId must be a string',
          'string.empty': 'documentId cannot be empty',
          'any.required': 'documentId is required',
        }),
      email: Joi.string().email({ minDomainSegments: 2 }).required().messages({
        'string.base': 'Your email must be a string',
        'string.email': 'Please enter a valid email address',
        'string.empty': 'Customer email address is missing',
        'any.required': 'Customer email address is missing',
      }),
      data: contentSchema.required().messages({
        'any.required': 'Data is required.',
      }),
    });

    const data = JSON.parse(JSON.stringify(req.body));
    req.body = JSON.parse(JSON.stringify(req.body));
    const requestBodyData = decoupleEntities(req.body.data);
    if (!requestBodyData) return;
    req.body.data = requestBodyData;

    const { error } = schema.validate(req.body);
    if (error) {
      logger.error(error.message);
      return next(new AppError(error.details[0].message.replace(/"+/g, ''), 400));
    }

    if (!isAllowedDocument(req.body.data.documentType)) {
      throw new BadRequestError(Errors.INVALID_DOCUMENT_TYPE);
    }

    if (!isAllowedDocumentEntity(req.body.data.entityType)) {
      throw new BadRequestError(Errors.INVALID_ENTITY);
    }

    const { documentType, entityType } = req.body.data;

    req.body.data.documentType = trimAndLowerCase(documentType);
    req.body.data.entityType = trimAndLowerCase(entityType);
    data.data.bgColorOpacity = convertHexToRgba(data.data.bgColor, 0.1);
    const isServiceEntity = req.body.data.entityType === 'service';
    const { entityData, amounts, totalFees, totalVat, totalDiscount } =
      calcEntityAmountAndGetAmounts(req.body.data, req.body.data.entityType);
    const subTotalAmount = calcSubTotalAmount(isServiceEntity ? totalFees : amounts);
    const totalAmount = calcTotalAmount(subTotalAmount, totalVat, totalDiscount);
    if (totalAmount === 0) throw new BadRequestError(Errors.AMOUNT_IS_ZERO);
    data.data.entities = formatAllEntities(entityData, req.body.data.entityType);
    data.data.totalVat = formatNumberWithCommas(totalVat);
    data.data.totalDiscount = formatNumberWithCommas(totalDiscount);
    data.data.totalAmount = formatNumberWithCommas(totalAmount);
    data.data.subTotalAmount = formatNumberWithCommas(subTotalAmount);
    data.data.amountPaid = formatNumberWithCommas(data.data.amountPaid);
    data.data.dateIssued = formatDateToDayMonthYear(data.data.dateIssued, req.userTimezone);
    data.data.dueDate = formatDateToDayMonthYear(data.data.dueDate, req.userTimezone);
    data.data.documentType =
      data.data.documentType.charAt(0).toUpperCase() + data.data.documentType.slice(1);
    req.body = data;

    next();
  }
);
