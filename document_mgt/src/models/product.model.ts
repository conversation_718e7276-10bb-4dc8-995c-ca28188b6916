import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';
import { INVENTORY_STATUS, INVENTORY_STATUS_ARRAY } from '../constants/values.constants';
import { Models } from './associations.models';
import ProductCategory, { ProductCategoryAttributes } from './product-category.model';

export interface ProductAttributes {
  id?: string;
  organization_id?: string;
  category_id?: string;
  name: string;
  description: string;
  units: number;
  price: number;
  image_url: string;
  status: string;
  category?: ProductCategoryAttributes | ProductCategory;

  createdAt?: Date;
  updatedAt?: Date;
}

class Product extends Model<ProductAttributes> implements ProductAttributes {
  declare id?: string;
  declare organization_id?: string;
  declare category_id?: string;
  declare name: string;
  declare description: string;
  declare units: number;
  declare price: number;
  declare image_url: string;
  declare status: string;
  declare category?: ProductCategoryAttributes | ProductCategory;

  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;

  // declare getCategories: BelongsToManyGetAssociationsMixin<ProductCategory>;
  // declare addCategory: BelongsToManyAddAssociationMixin<ProductCategory, string>;
  // declare addCategories: BelongsToManyAddAssociationsMixin<ProductCategory, string>;
  // declare setCategories: BelongsToManySetAssociationsMixin<ProductCategory, string>;
  // declare removeCategory: BelongsToManyRemoveAssociationMixin<ProductCategory, string>;
  // declare removeCategories: BelongsToManyRemoveAssociationsMixin<ProductCategory, string>;
  // declare hasCategory: BelongsToManyHasAssociationMixin<ProductCategory, string>;
  // declare hasCategories: BelongsToManyHasAssociationsMixin<ProductCategory, string>;

  static associate(models: Models) {
    Product.belongsTo(models.ProductCategory, {
      // through: ProductCategoryAssociation,
      as: 'category',
      foreignKey: 'category_id',
      // otherKey: 'category_id',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Product.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    organization_id: {
      type: DataTypes.STRING(30),
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    category_id: {
      type: DataTypes.UUID,
      defaultValue: null,
      allowNull: true,
      references: { model: 'product_categories', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING(30),
      allowNull: false,
    },
    description: { type: DataTypes.TEXT(), defaultValue: '', allowNull: true },
    units: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 },
    price: { type: DataTypes.DECIMAL(12, 2), allowNull: false },
    image_url: { type: DataTypes.STRING, allowNull: true, defaultValue: '' },
    status: {
      type: DataTypes.ENUM(...INVENTORY_STATUS_ARRAY),
      allowNull: false,
      defaultValue: INVENTORY_STATUS.active,
    },
  },
  {
    sequelize,
    modelName: 'Product',
    tableName: 'products',
    timestamps: true,
  }
);

export default Product;
