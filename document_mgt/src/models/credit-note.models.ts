import { DataTypes, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import Document from './documents.model';
import { Models } from './associations.models';

class CreditNote extends Document {
  public creditNoteNumber!: string;

  static associate(models: Models) {
    CreditNote.belongsTo(models.Invoice, {
      //as: 'invoice',
      foreignKey: 'invoiceId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    CreditNote.belongsTo(models.Document, {
      //as: 'document',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

CreditNote.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    creditNoteNumber: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    docId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'documents', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    invoiceId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'invoices', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    dateIssued: { type: DataTypes.DATE, allowNull: true },
    dueDate: { type: DataTypes.DATE, allowNull: true },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    modelName: 'creditNote',
    tableName: 'creditNotes',
  }
);

export default CreditNote;
