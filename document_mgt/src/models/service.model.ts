import { DataTypes, Model, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import { Models } from './associations.models';
// import User from './user.model';

export interface ServiceAttributes {
  id?: string;
  orgId: string;
  docId?: string;
  type?: string;
  description?: string;
  hours?: number;
  hourlyRate?: number;
  discount?: number;
  totalFee?: number;
  vat?: number;
  createdAt: Date;
  updatedAt: Date;
}

class Services extends Model<ServiceAttributes> implements ServiceAttributes {
  public id!: string;
  public orgId!: string;
  public docId!: string;
  public type!: string;
  public description!: string;
  public hours!: number;
  public hourlyRate!: number;
  public discount!: number;
  public vat!: number;
  public totalFee!: number;

  public createdAt: Date;
  public updatedAt: Date;

  static associate(models: Models) {
    Services.belongsTo(models.Document, {
      //as: 'document',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}
Services.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    docId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'documents', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    hours: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    hourlyRate: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    discount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    vat: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    totalFee: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    modelName: 'service',
    tableName: 'services',
  }
);
export default Services;
