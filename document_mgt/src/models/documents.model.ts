import { DataTypes, Model, Sequelize } from 'sequelize';
import sequelize from '../config/database/connection';
import { DOCUMENT_TYPE } from '../constants/values.constants';
// import { truncateToTwoDecimals } from '../utilities/global.utilities';
import { Models } from './associations.models';
import { CustomerAttributes } from './customer.model';
import { ItemAttributes } from './item.model';
import { ServiceAttributes } from './service.model';

export interface DocumentAttributes {
  id?: string;
  orgId?: string;
  customerId?: string;
  docId?: string;
  type?: string;
  entityType?: string;
  businessName?: string;
  businessAddress?: string;
  businessCountry?: string;
  totalDiscount?: number;
  notes?: string;
  totalVat?: number;
  amount?: number;
  totalAmount?: number;
  remainingPayableAmount?: number;
  creditNoteAmount?: number;
  refundableAmount?: number;
  amountPaid?: number;
  companyRegistrationNumber?: string;
  vatNumber?: string;
  taxNumber?: string;
  subTotalAmount?: number;
  creditNoteNumber?: string;
  receiptNumber?: string;
  invoiceNumber?: string;
  documentNumber?: string;
  invoiceId?: string;
  refunded?: boolean;
  archive?: boolean;
  logo?: string;
  logoThumbnail?: string;
  status?: string;
  dateIssued?: Date | null;
  dueDate?: Date | null;

  Customer?: CustomerAttributes;
  Items?: ItemAttributes[];
  Services?: ServiceAttributes[];

  createdAt?: Date;
  updatedAt?: Date;
}

class Document extends Model<DocumentAttributes> implements DocumentAttributes {
  public id!: string;
  public orgId!: string;
  public customerId!: string;
  public docId!: string;
  public logo!: string;
  public logoThumbnail!: string;
  public type!: string;
  public entityType!: string;
  public businessName!: string;
  public businessAddress!: string;
  public businessCountry!: string;
  public documentNumber!: string;
  public totalDiscount!: number;
  public notes!: string;
  public totalVat!: number;
  public totalAmount!: number;
  public remainingPayableAmount!: number;
  public refundableAmount!: number;
  public creditNoteAmount!: number;
  public amountPaid!: number;
  public subTotalAmount!: number;
  public amount!: number;
  public refunded!: boolean;
  public archive!: boolean;
  public companyRegistrationNumber!: string;
  public vatNumber!: string;
  public taxNumber!: string;

  public status!: string;

  public dueDate!: Date | null;
  public dateIssued!: Date | null;

  // Customer?: CustomerAttributes;
  Items?: ItemAttributes[];
  Services?: ServiceAttributes[];

  public createdAt: Date;
  public updatedAt: Date;

  static associate(models: Models) {
    Document.hasOne(models.Invoice, {
      //as: 'invoice',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasOne(models.Receipt, {
      //as: 'receipt',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasOne(models.CreditNote, {
      //as: 'creditNote',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.belongsTo(models.Customer, {
      //as: 'customer',
      foreignKey: 'customerId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasMany(models.Item, {
      //as: 'items',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Document.hasMany(models.Service, {
      //as: 'services',
      foreignKey: 'docId',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Document.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      primaryKey: true,
    },
    orgId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    customerId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'customers', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    docId: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    logo: DataTypes.STRING,
    logoThumbnail: DataTypes.STRING,
    type: DataTypes.STRING,
    entityType: {
      type: DataTypes.ENUM('product', 'service'),
      allowNull: false,
    },
    businessName: DataTypes.STRING,
    businessAddress: DataTypes.STRING,
    businessCountry: DataTypes.STRING,
    documentNumber: DataTypes.STRING,
    notes: DataTypes.STRING,
    companyRegistrationNumber: DataTypes.STRING,
    vatNumber: DataTypes.STRING,
    taxNumber: DataTypes.STRING,
    status: {
      type: DataTypes.ENUM('draft', 'partial payment', 'awaiting payment', 'completed'),
      allowNull: false,
      defaultValue: 'draft',
    },
    totalDiscount: DataTypes.FLOAT,
    totalVat: DataTypes.FLOAT,
    totalAmount: DataTypes.FLOAT,
    remainingPayableAmount: DataTypes.FLOAT,
    refundableAmount: DataTypes.FLOAT,
    creditNoteAmount: DataTypes.FLOAT,
    subTotalAmount: DataTypes.FLOAT,
    amountPaid: DataTypes.FLOAT,
    refunded: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    archive: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    dateIssued: { type: DataTypes.DATE, allowNull: true },
    dueDate: { type: DataTypes.DATE, allowNull: true },
    createdAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
    updatedAt: { type: DataTypes.DATE, defaultValue: Sequelize.fn('now') },
  },
  {
    sequelize,
    indexes: [{ fields: ['type'] }],
    modelName: 'Document',
    tableName: 'documents',
  }
);

Document.addHook('afterFind', async (document: any) => {
  if (document && document.type === DOCUMENT_TYPE.INVOICE) {
    const creditNoteAmount = document.creditNoteAmount;
    const totalAmount = document.totalAmount;
    document.creditNoteIssuableAmount = totalAmount - creditNoteAmount;
  }
});
export default Document;
