import {
  Model,
  FindOptions,
  WhereOptions,
  Attributes,
  ModelStatic,
  BulkCreateOptions,
} from 'sequelize';
import { <PERSON>rrorWrapper } from '../helpers/class.helpers';
import { NotFoundError } from '../middlewares/error_handlers/app-error';

export default class BaseServices<T extends Model> extends ErrorWrapper {
  constructor(
    protected model: ModelStatic<T>,
    protected cacheServiceName?: string
  ) {
    super();
  }

  protected async getOne(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = true
  ): Promise<Attributes<T> | T> {
    const data = await this.model.findOne({ where, ...options });

    if (!data) throw new NotFoundError(`${String(this.model.name).toLowerCase()} not found`);

    return plain ? data.get({ plain: true }) : data;
  }

  protected async getMany(
    where: WhereOptions<Attributes<T>>,
    options: FindOptions = {},
    plain: boolean = true
  ): Promise<{ rows: (Attributes<T> | T)[]; count: number }> {
    const result = await this.model.findAndCountAll({ where, ...options });
    return {
      rows: plain ? result.rows.map((r) => r.get({ plain: true })) : result.rows,
      count: result.count,
    };
  }

  protected async create(
    data: Partial<Attributes<T>>,
    plain: boolean = true
  ): Promise<Attributes<T> | T> {
    const instance = await this.model.create(data as any);
    return plain ? instance.get({ plain: true }) : instance;
  }

  protected async bulkCreate(
    data: Partial<Attributes<T>>[],
    options: BulkCreateOptions = {},
    plain: boolean = true
  ): Promise<(Attributes<T> | T)[]> {
    const created = await this.model.bulkCreate(data as any, { ...options });
    return plain ? created.map((i) => i.get({ plain: true })) : created;
  }

  // protected async bulkCreate(
  //   data: Partial<Attributes<T>>[],
  //   options: Omit<Parameters<ModelStatic<T>['bulkCreate']>[1], 'returning'> & {
  //     plain?: boolean;
  //   } = {}
  // ): Promise<(Attributes<T> | T)[]> {
  //   const { plain = true, ...sequelizeOptions } = options;

  //   const instances = await this.model.bulkCreate(data as any[], {
  //     ...sequelizeOptions,
  //     returning: true,
  //   });

  //   return plain ? instances.map((i) => i.get({ plain: true })) : instances;
  // }

  // public async update(
  //   where: WhereOptions<Attributes<T>>,
  //   updates: Partial<Attributes<T>>,
  //   options: FindOptions = {}
  // ): Promise<number> {
  //   const [affectedCount] = await this.model.update(updates, {
  //     where,
  //     ...options,
  //   });

  //   if (affectedCount === 0) {
  //     throw new NotFoundError(`${this.model.name} not found`);
  //   }

  //   return affectedCount;
  // }

  // public async delete(
  //   where: WhereOptions<Attributes<T>>,
  //   options: FindOptions = {}
  // ): Promise<number> {
  //   const count = await this.model.destroy({ where, ...options });
  //   if (count === 0) {
  //     throw new NotFoundError(`${this.model.name} not found for delete`);
  //   }
  //   return count;
  // }

  // // Cache Methods
  // public async clearCachedData(organizationId: number | string): Promise<void> {
  //   await clearCache(organizationId, this.cacheServiceName);
  // }

  // public async cacheGetAll(
  //   organizationId: number | string,
  //   response: { msgAndCode: [string, number]; data?: any; meta?: any },
  //   offset: number,
  //   limit: number,
  //   timezone: string,
  //   employeeIsPresent?: number | string | false
  // ): Promise<any> {
  //   return cache(
  //     organizationId,
  //     this.cacheServiceName,
  //     response,
  //     offset,
  //     limit,
  //     timezone,
  //     employeeIsPresent
  //   );
  // }
}
