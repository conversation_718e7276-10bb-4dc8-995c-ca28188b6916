// import puppeteer from 'puppeteer';
import {
  ISendPDFDocumentData,
  ReminderInvoiceDocument,
} from '../../../../interfaces/documents.interfaces';
import { ErrorWrapper } from '../../../../helpers/class.helpers';

export default class PdfDeliveryUtils extends ErrorWrapper {
  static getCurrencyLabel(currency: string) {
    let label = '';
    if (currency === '$') {
      label = 'Routing Number (ABA Number)';
    } else if (currency === '£') {
      label = 'Sort Code';
    } else if (['€', 'د.إ'].includes(currency)) {
      label = 'BIC/SWIFT Code';
    }
    return label;
  }

  static async getPaymentInfo(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    const label = this.getCurrencyLabel(data.currency);
    const output = `${label}: ${data.sortCode}`;
    const sortCodeParagraph = data.sortCode
      ? `<p style="margin-top: 0.5rem; font-size: 12px; line-height: 16px;">
           ${output}
        </p>`
      : '';

    return `${
      data.documentType.toLowerCase() === 'invoice'
        ? `
            <div class="payment-info" style="color: #4c4d4d; ">
              <p style="font-weight: 700; margin-top: 0.5rem; font-size: 12px; line-height: 16px; color: ${data.bgColor}">Payment information</p>
                <p style="margin-top: 0.5rem; font-size: 12px; line-height: 16px;">Bank name: ${data.bankName}</p>
                <p style="margin-top: 0.5rem; font-size: 12px; line-height: 16px;">Account name: ${data.bankAccountName}</p>
                <p style="margin-top: 0.5rem; font-size: 12px; line-height: 16px;">Account no: ${data.bankAccountNumber}</p>
                 ${sortCodeParagraph}
            </div>`
        : ''
    }`;
  }

  static async getCreditDetails(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    return `${
      data.documentType.toLowerCase() === 'credit note'
        ? `
           <div class="document">
                    <p style="margin-top: 0.25rem; margin-bottom: 0.5rem; font-size: 12px; line-height: 16px; font-weight: 700;">
                      Credit details
                    </p>
                    <p style="margin-top: 0.25rem; margin-bottom: 0.5rem; font-size: 12px; line-height: 16px;">
                      Credit date: ${data.dateIssued}
                    </p>

                    <p style="margin-top: 0.25rem; margin-bottom: 0.5rem; font-size: 12px; line-height: 16px;">
                      Original invoice: ${data.invoiceNumber}
                    </p>

                  </div>`
        : ''
    }`;
  }

  static async getReceiptHeader(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    return `  ${
      data.documentType.toLowerCase() === 'receipt'
        ? `
             <header id="header" style="padding: 3.5rem 3.5rem 0 3.5rem;">
               <div style="margin-bottom: 0.5rem;">
                   <span style="display: flex; gap: 0.25rem; align-items: center; margin-bottom: 1rem;">
                    ${
                      data.logo
                        ? `<img src="${data.logo}" width="64" alt="logo" />`
                        : data.businessName
                    }
                 </span>

                 <span style="font-weight: bold; color: #4c4d4d;">
                   <p style="color: #4c4d4d; font-size: 24px; font-weight: bold;">
                     Receipt
                   </p>
                   <p style="color: #4c4d4d; font-size: 1rem; line-height: 1.75rem; font-weight: bold;">
                    ${data.documentType} no: ${data.documentNumber}
                  </p>
                 </span>

               </div>
               <div style="font-size: 0.75rem; color: #4c4d4d;">
                 <p style="font-weight: bold;">Receipt details</p>
                 <p style="margin: 0.5rem 0;">
                   Receipt date: <span style="margin-left: 0.25rem;">${data.dateIssued}</span>
                 </p>
               </div>
               <div style="margin: 2.5rem 0; display: flex; gap: 1rem;">
                 <span style="flex: 1; color: #000000;">
                   <p style="color: #979999; font-size: 0.75rem;">From:</p>
                   <p style="font-size: 0.875rem; line-height: 1.25rem; font-weight: bold; margin: 0.25rem 0;">
                     ${data.businessName}
                   </p>
                   <p style="font-size: 0.75rem; line-height: 0.75rem; max-width: 195px;">
                     ${data.businessAddress}
                   </p>
                 </span>
                 <span style="flex: 1; color: #000000;">
                   <p style="color: #979999; font-size: 0.75rem;">To:</p>
                   <p style="font-size: 0.875rem; line-height: 1.25rem; font-weight: bold; margin: 0.25rem 0;">
                     ${data.customerName}
                   </p>
                   <p style="font-size: 0.75rem; line-height: 0.75rem; max-width: 195px;">
                     ${data.customerAddress}
                   </p>
                 </span>
               </div>
             </header>
            `
        : ''
    }`;
  }

  static getDocumentNote(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    return `${
      data.notes
        ? `
                    <div style="padding: 1.5rem 0; ">
                      <p style="font-size: 0.75rem; font-weight: 600; color: #979999;">
                       Notes
                      </p>
                      <p style="font-size: 0.75rem; color: #000000;">
                       ${data.notes}
                       </p>
                  </div>
                  `
        : ``
    }`;
  }

  static async getReceiptFooter(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    return `  ${
      data.documentType.toLowerCase() === 'receipt'
        ? `
            <div class="receipt-amount">
                 <div style="color: #4C4D4D; padding: 1.5rem 4rem; background-color:white;">
                    <div style="display: flex; justify-content: flex-end;">
                      <div style="text-align: right;">
                        <p style="font-size: 1rem; line-height: 1.5rem; font-weight: bold; margin-bottom: 0.5rem; color: #4c4d4d;">
                         Amount Paid
                        </p>
                        <p style="font-size: 1.5rem; line-height: 2.5rem; font-weight: bold; color: #4c4d4d;">
                         ${data.currency} ${data.amountPaid}
                        </p>
                      </div>
                    </div>
              <div margin-bottom: 12px">
                <p style="font-size: 12px; margin-bottom: 0.25rem; color: #979999">
                  Original invoice:
                </p>
                <p style="font-size: 14px; font-weight: bold; color: ${data.bgColor}">
                ${data.invoiceNumber}
                </p>
              </div>
                 </div>
                  <p class="created-by" style="text-align: center; font-size: 10px;">This receipt was created on digit-tally.io</p>
               </div>`
        : ''
    }`;
  }

  static async getDeliveryEntitiesHTML(entities, entityType) {
    const isServiceEntity = entityType === 'service';

    let data = '';

    for (const entity of entities) {
      data += `
      <tbody>
      <tr style="color: #4c4d4d; font-size: 12px; line-height: 1rem; padding: 0 3rem; display: grid; grid-template-columns: repeat(7, minmax(0, 1fr));">
          <td style="grid-column: span 2 / span 2; text-align: left; padding: 12px 4px; overflow-wrap: break-word;">
            <p style="color: #0f0f0f; font-weight: 600; margin-bottom: 4px">
              ${isServiceEntity ? entity.type : entity.name}
            </p>
            <p>${entity.description}</p>
          </td>
          <td style="font-weight: 400; text-align: right; padding: 12px 4px; overflow-wrap: break-word;">
             ${isServiceEntity ? entity.hours || 0 : entity.quantity}
          </td>
          <td style="font-weight: 400; text-align: right; padding: 12px 4px; overflow-wrap: break-word;">
            ${isServiceEntity ? entity.hourlyRate || 0 : entity.unitPrice}
          </td>
          <td style="font-weight: 400; text-align: right; padding: 12px 4px; overflow-wrap: break-word;">
            ${entity.discount}
          </td>
          <td style="font-weight: 400; text-align: right; padding: 12px 4px; overflow-wrap: break-word;">
            ${entity.vat}
          </td>
          <td style="font-weight: 400; text-align: right; padding: 12px 4px; overflow-wrap: break-word;">
          ${isServiceEntity ? entity.totalFee : entity.amount}
          </td>
        </tr>
        </tbody>
            `;
    }
    return data;
  }

  static async getDeliveryTableHeaderHTML(entityType, currency, bgColorOpacity) {
    const isServiceEntity = entityType === 'service';
    const headerBgColor = bgColorOpacity;

    return `
    <thead style="background: ${headerBgColor};">
      <tr style="font-size: 12px; color: #4c4d4d; display: grid; grid-template-columns: repeat(7, minmax(0, 1fr)); padding: 8px 3rem;">
        <th style="padding: 4px; text-align: left; grid-column: span 2">
          Item
        </th>
        <th style="padding: 4px; text-align: right">
          ${isServiceEntity ? 'Hours' : 'Quantity'}
        </th>
        <th style="padding: 4px; text-align: right; font-size: 10px;">
          ${isServiceEntity ? 'Hourly rate' : 'Unit price'} (${currency})
        </th>
        <th style="padding: 4px; text-align: right">Discount (%)</th>
        <th style="padding: 4px; text-align: right">Tax rate (%)</th>
        <th style="padding: 4px; text-align: right">
          ${isServiceEntity ? 'Total fee' : 'Amount'} (${currency})
        </th>
      </tr>
    </thead>
  `;
  }

  static async getDeliveryHeaderHTML(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    return `
      ${
        data.documentType.toLowerCase() !== 'receipt'
          ? `
          <header id="header" style="padding: 3.5rem 1.5rem 0 1.5rem;">
            <div class="details1" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 1rem; padding: 0 1.5rem;">
            <div> 
            <span class="logo-container" style="display: flex; gap: 0.25rem; align-items: center;">
                 ${
                   data.logo
                     ? `<img src="${data.logo}" width="64" alt="logo" />`
                     : data.businessName
                 }
              </span>
              ${
                data.companyRegistrationNumber
                  ? `
                <p style="color: #4c4d4d; font-size: 12px; line-height: 1.25rem; font-weight: bold; margin-top: 4px;">
                  Company number: ${data.companyRegistrationNumber}
                </p>
              `
                  : ''
              }
              
            </div>
            <div class="document-name" style="text-align: right; font-weight: bold; color: #4c4d4d;">
              <p style="color: #4c4d4d; font-size: 1.25rem; line-height: 1.75rem; font-weight: bold;">
                  ${data.documentType}${
                    data.documentType.toLowerCase() === 'invoice' ? `: ${data.documentNumber}` : ''
                  }
              </p>
              ${
                data.taxNumber
                  ? `
                <p style="color: #4c4d4d; font-size: 12px; line-height: 1.25rem; font-weight: bold; margin: 4px 0;">
                  Tax ID: ${data.taxNumber}
                </p>
              `
                  : ''
              }
              
              ${
                data.vatNumber
                  ? `
                <p style="color: #4c4d4d; font-size: 12px; line-height: 1.25rem; font-weight: bold;">
                  VAT number: ${data.vatNumber}
                </p>
              `
                  : ''
              }
              
            </div>
            </div>
            <div class="details2" style="margin: 2.5rem 0; padding: 0 1.5rem; display: flex; justify-content: space-between;">
              <!-- Billed to section on the left -->
              <div class="customer" style="color: #000000;">
                <p style="color: #979999; font-size: 0.75rem;">Billed to:</p>
                <p style="font-size: 0.875rem; line-height: 1.25rem; font-weight: bold; margin: 0.25rem 0; color: ${
                  data.bgColor
                };">
                  ${data.customerName}
                </p>
                <p style="font-size: 0.75rem; line-height: 0.75rem; max-width: 195px;">
                  ${data.customerAddress}
                </p>
              </div>
              <!-- Date section on the right -->
              <div class="date" style="color: #000000;">
                <div>
                  <p style="color: #979999; font-size: 0.75rem;">Date issued:</p>
                  <p style="font-size: 0.875rem; line-height: 1.25rem; font-weight: bold; margin: 0.25rem 0; color: ${
                    data.bgColor
                  };">
                    ${data.dateIssued}
                  </p>
                </div>
                ${
                  data.documentType.toLowerCase() === 'invoice'
                    ? `<div>
                  <p style="color: #979999; font-size: 0.75rem;">Due date:</p>
                  <p style="font-size: 0.875rem; line-height: 1.25rem; font-weight: bold; margin: 0.25rem 0; color: ${
                    data.bgColor
                  };">
                    ${data.dueDate}
                  </p>`
                    : ` `
                }
                </div>
              </div>
            </div>
          </header>
            `
          : // Receipt header
            `
          ${await this.getReceiptHeader(data)}
          `
      }
  
    `;
  }

  static async getDeliveryFooterHTML(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    return `
    <footer id="footer" style="font-family: ${data.font};">
    <div style="display: flex; justify-content: space-between; align-items: center; color: #0F0F0F; padding: 1rem 4rem; font-size: 12px; line-height: 16px; background: ${
      data.bgColorOpacity
    };">

      <section style="width: 50%; ">
          <!-- Payment Info section -->
          ${await this.getPaymentInfo(data)}
      </section>
  
<section style="display: flex; justify-content: flex-end; font-size: 0.75rem; width: 100%;">
  <!-- Labels column -->
  <div style="font-size: 0.875rem; display: flex; flex-direction: column; align-items: flex-end; gap: 0.25rem;">
    <p style="margin-right: 16px;">Sub total (excl. deductions)</p>
    <p style="margin-right: 16px;">Total discount</p>
    <p style="margin-right: 16px;">Total VAT</p>
    ${
      data.documentType.toLowerCase() !== 'receipt'
        ? `<hr style="border: none; height: 1px; width: 100%; background-color: ${data.bgColor};" />`
        : ''
    }
    <p style="margin-top: 0.25rem; margin-right: 16px;">${
      data.documentType.toLowerCase() === 'invoice'
        ? 'Balance due'
        : data.documentType.toLowerCase() === 'credit note'
          ? 'Credit note total'
          : 'Total amount'
    }</p>
  </div>

  <!-- Values column -->
  <div style="display: flex; flex-direction: column; align-items: flex-end; font-size: 0.875rem; font-weight: bold; color: ${
    data.bgColor
  }; gap: 0.25rem;">
    <p>${data.currency} ${data.subTotalAmount}</p>
    <p style="color: red;">- ${data.currency} ${data.totalDiscount}</p>
    <p>${data.currency} ${data.totalVat}</p>
    ${
      data.documentType.toLowerCase() !== 'receipt'
        ? `<hr style="border: none; height: 1px; width: 100%; background-color: ${data.bgColor};" />`
        : ''
    }
    <p style="margin-top: 0.25rem;">${data.currency} ${data.totalAmount}</p>
  </div>
</section>

      </div>
  
      <!-- Original invoice for credit note -->
      ${
        data.documentType.toLowerCase() === 'credit note'
          ? `
          <div style="margin: 1.1rem 4rem 0;">
            <p style="font-size: 12px; color: #979999">Original invoice:</p>
            <p style="font-size: 14px; font-weight: bold; color: ${data.bgColor}">${data.invoiceNumber}</p>
          </div>`
          : ''
      }
  
      <!-- Notes -->
      ${
        data.documentType.toLowerCase() !== 'receipt'
          ? `
          ${
            data.notes
              ? `
                <div style="padding: 1rem 0 0.6rem 4rem; font-size: 12px; line-height: 16px; word-wrap: break-word; max-width: 400px;">
                  <p style="font-weight: 600; color: #979999;">Notes</p>
                  <p style="color: #0f0f0f;">${data.notes}</p>
                </div>
              `
              : data.documentType.toLowerCase() === 'invoice'
                ? `
                <div style="padding: 1.37rem 4rem; font-size: 12px; line-height: 16px;">
                  <p style="font-weight: 600; color: #979999; margin-bottom: 0.25rem;"></p>
                  <p style="color: #0f0f0f;"></p>
                </div>`
                : ''
          }
  
          <!-- Created on -->
          <div style="background: ${
            data.bgColor
          }; padding: 24px 4rem; margin-top: 16px; display: flex; align-items: center; justify-content: space-between;">
            <img src="${data.logo}" width="64" alt="logo" />
            <p style="font-size: 10px; color: #ffffff; font-weight: 700;">
              This ${data.documentType.toLowerCase()} was created on digit-tally.io
            </p>
          </div>
        `
          : `
          <!-- Receipt footer -->
          ${await this.getReceiptFooter(data)}
        `
      }
    </footer>`;
  }

  static async getDeliveryHTML(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    if (data.documentType.toLowerCase() === 'creditnote') {
      data.documentType = 'Credit Note';
    }
    return `<!DOCTYPE html>
      <html lang="en">
          <head>
              <meta charset="UTF-8" />
              <meta name="viewport" content="width=device-width, initial-scale=1.0" />
              <title>${data.businessName} ${data.documentType}</title>
              <script src="https://cdn.tailwindcss.com"></script>
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
              <link
                href="https://fonts.googleapis.com/css2?family=Spline+Sans:wght@300..700&display=swap"
                rel="stylesheet" />
          </head>

      <body>
        <main style="font-family: ${data.font}; overflow-y: auto; color: #0f0f0f;">
          <!-- Beginning of Header section -->
            ${await this.getDeliveryHeaderHTML(data)}
          <!-- End of Header section -->

          <!-- Beginning of Entities section -->
          <table style="height: auto; width: 100%">
            ${await this.getDeliveryTableHeaderHTML(
              data.entityType,
              data.currency,
              data.bgColorOpacity
            )}
            ${await this.getDeliveryEntitiesHTML(data.entities, data.entityType)}
          </table>
          <!-- End of Entities section -->
        </main>
        </body>
      </html>`;
  }
}
