import Document, { DocumentAttributes } from '../../models/documents.model';
import { Request, Response } from 'express';
import InvoiceServices from './invoice.services';
import ReceiptServices from './receipt.services';
import CreditNoteServices from './credit-note.services';
import Invoice from '../../models/invoice.models';
import Customer from '../../models/customer.model';
import CustomerUtils from '../customers/customer-utils.services';
import ItemServices from '../items/items.services';
import CustomerServices from '../customers/customer.services';
import DocumentUtils from './utils/documents.utils';
import PdfDeliveryUtils from './utils/html/delivery';
import {
  formatDateToYearMonthDayTime,
  getFileName,
  getTemplateName,
  incrementDocumentId,
  removeWhiteSpace,
  renderTemplate,
  trimAndLowerCase,
  truncateToTwoDecimals,
} from '../../utilities/global.utilities';
import { EmailFeatures } from '../../utilities/email.utilities';
import { Errors } from '../../constants/errors.constants';
import { isSuperAdmin, isValuePresent } from '../../utilities/guards';
import {
  BadRequestError,
  ConflictError,
  NotFoundError,
  NotPermittedError,
} from '../../middlewares/error_handlers/app-error';
import ServiceServices from '../service/service.services';

import {
  DOCUMENT_STATUS,
  DOCUMENT_TYPE,
  FILE_EXTENSION,
  ZERO,
} from '../../constants/values.constants';
import { IDocument } from '../../types/document';
import InvoiceUtils from './utils/invoice-utils.services';
import {
  ReminderInvoiceDocument,
  IDownloadDocumentQueryParam,
  ISendPDFDocumentData,
  ISendPDFTemplateData,
} from '../../interfaces/documents.interfaces';
import UtilityAPIS from '../../api/utilities';
import { IAuthenticatedUser } from '../../interfaces/user.interfaces';
import { SendInvoiceReminderPayload } from '../../interfaces/request-body/document-payload.interfaces';
import BaseServices from '../base.service';
import { WhereOptions } from 'sequelize';
import {
  formatDocumentFiguresWithComma,
  getReminderDocumentFullDetails,
  getReminderDocumentOptions,
  getReminderInvoiceTemplateData,
} from './utils/helper.utils';
import { EMAIL_SUBJECTS, EMAIL_TEMPLATES } from '../../constants/email.constants';

export default class DocumentServices extends BaseServices<Document> {
  constructor(
    private readonly invoiceService: InvoiceServices,
    private readonly receiptService: ReceiptServices,
    private readonly creditNoteService: CreditNoteServices,
    private readonly itemService: ItemServices,
    private readonly customerService: CustomerServices,
    private readonly serviceService: ServiceServices
  ) {
    super(Document);
  }

  public async getInvoiceDocument(id: string): Promise<any> {
    const invoice: Invoice = await Invoice.findOne({
      where: { invoiceNumber: id },
    });
    return invoice;
  }

  public async createInvoice(payload: Record<string, any>) {
    const customer: Customer = await CustomerUtils.getCustomerByOrgId(
      payload.customerId,
      payload.orgId
    );

    if (!customer) {
      throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);
    }

    const responseData = await DocumentUtils.createInvoiceAndDocument(
      payload,
      this.invoiceService,
      this.itemService,
      this.serviceService
    );

    const data: Partial<IDocument> = { ...responseData.dataValues };
    const {
      name: customerName,
      address: customerAddress,
      phoneNumber: customerPhoneNumber,
    } = customer;
    data.docId = responseData.id;
    data.customerName = customerName;
    data.customerAddress = customerAddress;
    data.customerPhoneNumber = customerPhoneNumber;
    data.invoiceId = responseData.invoiceId;
    return data;
  }

  public async createReceipt(receiptDTO: Record<string, any>) {
    const invoiceDocument = await this.getDocumentByInvoice(receiptDTO.invoiceId, receiptDTO.orgId);
    if (!invoiceDocument) return;

    if (invoiceDocument.status === DOCUMENT_STATUS.DRAFT) {
      throw new BadRequestError(Errors.INVALID_DOCUMENT);
    }
    if (invoiceDocument.archive) {
      throw new NotPermittedError(Errors.DOCUMENT_IS_ARCHIVED);
    }

    const totalAmountPaid = receiptDTO.amountPaid + invoiceDocument.amountPaid;
    const payableAmount: number = invoiceDocument.remainingPayableAmount;

    if (payableAmount === ZERO) {
      throw new BadRequestError(Errors.PAYMENT_COMPLETED);
    }

    if (receiptDTO.amountPaid > payableAmount)
      throw new ConflictError(`you can't pay beyond ${payableAmount}`);

    const { lastDocumentRegNumber: LastReceiptDocNumber } =
      await DocumentUtils.getLastDocumentRegNumber(receiptDTO.orgId, DOCUMENT_TYPE.RECEIPT);

    const NextReceiptNumber: string = await incrementDocumentId('REC', LastReceiptDocNumber);

    invoiceDocument.amountPaid = totalAmountPaid;
    invoiceDocument.remainingPayableAmount = truncateToTwoDecimals(
      payableAmount - receiptDTO.amountPaid
    );
    invoiceDocument.updatedAt = receiptDTO.updatedAt;
    invoiceDocument.status =
      invoiceDocument.remainingPayableAmount === 0
        ? DOCUMENT_STATUS.COMPLETED
        : DOCUMENT_STATUS.PARTIAL_PAYMENT;

    const payload = {
      receiptNumber: NextReceiptNumber,
      orgId: receiptDTO.orgId,
      invoiceId: receiptDTO.invoiceId,
      createdAt: receiptDTO.createdAt,
      updatedAt: receiptDTO.updatedAt,
    };
    const newReceipt = await this.receiptService.createReceipt(payload);

    if (!newReceipt) return;
    const newDoc = await invoiceDocument.save();
    delete newDoc.dataValues.id;
    delete newDoc.dataValues.createdAt;
    delete newDoc.dataValues.updatedAt;
    delete newDoc.dataValues.dateIssued;
    delete newDoc.dataValues.dueDate;

    const receiptPayload: Record<string, any> = { ...newDoc.dataValues };
    receiptPayload.amountPaid = receiptDTO.amountPaid;
    receiptPayload.receiptId = newReceipt.id;
    receiptPayload.documentNumber = NextReceiptNumber;
    receiptPayload.type = DOCUMENT_TYPE.RECEIPT;
    receiptPayload.dateIssued = new Date();
    receiptPayload.createdAt = receiptDTO.createdAt;
    receiptPayload.updatedAt = receiptDTO.updatedAt;

    const responseData = await Document.create(receiptPayload);
    newReceipt.docId = responseData.id;
    await newReceipt.save();
    const data: Partial<IDocument> = { ...responseData.dataValues };
    const { dataValues: customerData } = receiptPayload['Customer'];
    data.customerName = customerData.customerName;
    data.customerAddress = customerData.customerAddress;
    data.customerPhoneNumber = customerData.customerPhoneNumber;
    data.invoiceNumber = newDoc.documentNumber;
    return data;
  }

  public async createCreditNote(payload: Record<string, any>, invoiceDocument: Document) {
    payload.dateIssued = payload.dateIssued ?? null;

    const invoiceAmount: number = invoiceDocument['creditNoteIssuableAmount'];

    if (payload.totalAmount > invoiceAmount)
      throw new ConflictError('the total amount is greater than the amount on invoice');

    const { lastDocumentRegNumber: LastCreditNoteDocNumber } =
      await DocumentUtils.getLastDocumentRegNumber(payload.orgId, DOCUMENT_TYPE.CREDIT_NOTE);

    const NextCreditNoteNumber: string = await incrementDocumentId('CRN', LastCreditNoteDocNumber);

    const res = DocumentUtils.calculateInvoiceCreditNoteDifference(
      invoiceDocument.dataValues,
      payload
    );

    invoiceDocument.refunded = res.refundStatus;
    invoiceDocument.updatedAt = payload.updatedAt;
    invoiceDocument.status = res.invoiceStatus;
    invoiceDocument.creditNoteAmount = payload.draft
      ? invoiceDocument.creditNoteAmount
      : truncateToTwoDecimals(invoiceDocument.creditNoteAmount + payload.totalAmount);
    invoiceDocument.remainingPayableAmount = truncateToTwoDecimals(res.remainingPayableAmount);
    invoiceDocument.refundableAmount = truncateToTwoDecimals(
      invoiceDocument.refundableAmount + res.refundable
    );
    payload.refunded = res.refundStatus;
    payload.remainingPayableAmount = truncateToTwoDecimals(res.remainingPayableAmount);
    payload.refundableAmount = truncateToTwoDecimals(res.refundable);
    payload.amountPaid = truncateToTwoDecimals(invoiceDocument.amountPaid);
    payload.creditNoteAmount = truncateToTwoDecimals(payload.totalAmount);
    DocumentUtils.populateCreditNoteData(payload, invoiceDocument, NextCreditNoteNumber);
    const creditNotePayload = {
      orgId: payload.orgId,
      dateIssued: payload.dateIssued || null,
      invoiceId: payload.invoiceId,
      creditNoteNumber: NextCreditNoteNumber,
      createdAt: payload.createdAt,
      updatedAt: payload.updatedAt,
    };
    const newCreditNote = await this.creditNoteService.createCreditNote(creditNotePayload);
    if (!newCreditNote) return;
    const responseData = await DocumentUtils.createAndUpdateDocument(
      payload,
      invoiceDocument,
      this.itemService,
      this.serviceService
    );
    if (!responseData) return;
    newCreditNote.docId = responseData.id;
    await newCreditNote.save();
    const data: Partial<IDocument> = { ...responseData.dataValues };
    const { dataValues: customerData } = invoiceDocument['Customer'];
    data.customerName = customerData.customerName;
    data.customerAddress = customerData.customerAddress;
    data.customerPhoneNumber = customerData.customerPhoneNumber;
    return data;
  }

  public async editInvoice(orgId: string, id: number, data: Record<string, any>) {
    if (!isValuePresent(data.customerId)) throw new BadRequestError(Errors.REQUIRE_CUSTOMER_ID);
    else {
      const customer = await this.customerService.getCustomer(data.customerId, orgId);
      if (!customer) throw new NotFoundError(Errors.CUSTOMER_NOT_FOUND);
    }

    const response = await this.editDocument(orgId, data, id);
    return response;
  }

  public async editCreditNote(orgId: string, id: number, data: Record<string, any>) {
    const invoiceDocument = await this.getDocumentByInvoice(data.invoiceId, orgId);
    const creditNoteIssuableAmount: number = invoiceDocument['creditNoteIssuableAmount'];
    if (data.totalAmount > creditNoteIssuableAmount)
      throw new ConflictError('the total amount is greater than the amount on invoice');

    if (!invoiceDocument) return;

    if (data.totalAmount > invoiceDocument.totalAmount) {
      throw new ConflictError('the total amount is greater than the amount on invoice');
    }
    const result = DocumentUtils.calculateInvoiceCreditNoteDifference(invoiceDocument, data);
    const remainingPayableAmount =
      invoiceDocument.remainingPayableAmount - (data.draft ? 0 : data.totalAmount);
    const refundable = invoiceDocument.refundable + (data.draft ? 0 : data.totalAmount);
    invoiceDocument.refunded = result.refundStatus;
    invoiceDocument.remainingPayableAmount = truncateToTwoDecimals(remainingPayableAmount);
    invoiceDocument.status = result.invoiceStatus;
    invoiceDocument.refundableAmount = truncateToTwoDecimals(refundable);
    invoiceDocument.updatedAt = data.updatedAt;
    invoiceDocument.creditNoteAmount = data.draft
      ? truncateToTwoDecimals(invoiceDocument.creditNoteAmount)
      : truncateToTwoDecimals(invoiceDocument.creditNoteAmount + data.totalAmount);
    data.refunded = result.refundStatus;
    data.remainingPayableAmount = truncateToTwoDecimals(remainingPayableAmount);
    data.refundableAmount = truncateToTwoDecimals(refundable);
    data.amountPaid = truncateToTwoDecimals(invoiceDocument.amountPaid);
    data.creditNoteAmount = truncateToTwoDecimals(data.totalAmount);

    if (data.status !== DOCUMENT_STATUS.DRAFT) {
      await invoiceDocument.save();
      data.status = 'completed';
    } else {
      data.status = 'draft';
    }

    const response = await this.editDocument(orgId, data, id);
    return response;
  }

  public async processSystemPaymentReceipt() {}

  private async editDocument(orgId: string, data: Record<string, any>, id: number) {
    const updatedDoc = await Document.update(data, {
      where: { id, orgId },
      returning: true,
    });
    const documents: Document[] = [updatedDoc[1][0]];
    const responseData = await DocumentUtils.attachLinkedData(
      orgId,
      documents,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    return responseData[0];
  }

  public async getDocuments(
    req: Request,
    orgId: string,
    paginate?: { offset?: number; limit?: number; page?: number }
  ) {
    const whereCondition = { orgId, archive: false };
    if (isSuperAdmin(req.user.role) && orgId !== req.user.organization.id)
      whereCondition.orgId = req.user.organization.id;
    const res = await DocumentUtils.getDocuments(
      whereCondition,
      paginate,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    if (!res) return;
    const { data, count } = res;
    return { data, count };
  }

  public async getAllDocuments(
    req: Request,
    orgId: string,
    paginate?: { offset?: number; limit?: number; page?: number }
  ): Promise<any> {
    const whereCondition = { orgId, archive: false };
    if (isSuperAdmin(req.user.role) && orgId !== req.user.organization.id)
      delete whereCondition.archive;
    // if (isSuperAdmin(req.user.role) && !req.query.orgId) {
    //   delete whereCondition.archive;
    //   delete whereCondition.orgId;
    // }

    const res = await DocumentUtils.getDocuments(
      whereCondition,
      paginate,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    if (!res) return;
    const { data, count } = res;
    return { data, count };
  }

  public async searchDocument(condition: any, paginate: Record<string, number>) {
    const res = await DocumentUtils.getDocuments(
      condition,
      paginate,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    if (!res) return;
    const { data, count } = res;
    return { data, count };
  }

  public async getArchiveDocuments(
    paginate?: Record<string, number>,
    orgId?: string
  ): Promise<any> {
    const whereCondition = { archive: true };
    orgId && (whereCondition['orgId'] = orgId);

    const documents: Document[] = await Document.findAll({
      where: whereCondition,
      ...paginate,
      attributes: ['id', 'documentNumber', 'customerId', 'dateIssued', 'dueDate', 'status', 'type'],
      include: [
        {
          model: Customer,
          attributes: ['name'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    const data = documents.map((doc) => {
      const json: any = doc.toJSON();
      const customerName = doc.dataValues['Customer']?.name;
      delete json?.Customer;

      return {
        ...json,
        customerName,
      };
    });
    const count = data.length;
    return { data, count };
  }

  public async getDraftDocument(orgId: string, id: string): Promise<any> {
    const filter = { where: { id, orgId, status: DOCUMENT_STATUS.DRAFT } };

    const document = await DocumentUtils.getDocument(filter);
    if (!document) throw new NotFoundError(Errors.DRAFT_DOCUMENT_NOT_FOUND);

    const data = await DocumentUtils.getDocumentData(
      orgId,
      document,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    return data;
  }

  public async getRecentDocuments(orgId: string): Promise<any> {
    const whereCondition = { archive: false };
    if (orgId) {
      whereCondition['orgId'] = orgId;
    }
    const documents: Document[] = await Document.findAll({
      where: whereCondition,
      order: [['createdAt', 'DESC']],
      limit: 6,
      attributes: [
        'id',
        'docId',
        'type',
        'entityType',
        'totalVat',
        'totalDiscount',
        'notes',
        'documentNumber',
        'customerId',
        'updatedAt',
        'createdAt',
        'dateIssued',
        'dueDate',
        'totalAmount',
        'status',
        'createdAt',
      ],
    });
    const data = await DocumentUtils.attachLinkedData(
      orgId,
      documents,
      this.itemService,
      this.serviceService,
      this.customerService
    );
    return data;
  }

  public async getDocumentByInvoice(id: number, orgId: string): Promise<any> {
    const invoice = await InvoiceUtils.getInvoiceByIDAndOrgId(id, orgId);
    if (!invoice) throw new NotFoundError(Errors.DOCUMENT_WITH_INVOICE_ID_NOT_FOUND);
    const document: Document = await Document.findOne({
      where: { type: DOCUMENT_TYPE.INVOICE, id: invoice['Document'].id, orgId },
      include: {
        model: Customer,
        attributes: {
          include: [
            ['name', 'customerName'],
            ['address', 'customerAddress'],
            ['phoneNumber', 'customerPhoneNumber'],
          ],
        },
      },
    });
    return document;
  }

  public async getFilteredDocument(
    orgId: string,
    condition: Record<string, any>,
    paginate: Record<string, number>
  ) {
    const documents = await DocumentUtils.filterDocument(condition.type, condition, paginate);

    const data = await DocumentUtils.attachLinkedData(
      orgId,
      documents.rows,
      this.itemService,
      this.serviceService,
      this.customerService
    );

    return { data, count: documents.count };
  }

  public async deleteDocument(id: string, orgId: string): Promise<any> {
    await Document.destroy({ where: { id, orgId } });
    return true;
  }

  public async convertDocumentToPDF(data: ISendPDFDocumentData | ReminderInvoiceDocument) {
    const bodyHtml = await PdfDeliveryUtils.getDeliveryHTML(data);
    const footerHtml = await PdfDeliveryUtils.getDeliveryFooterHTML(data);

    const documentType = data.documentType.toLowerCase().replace(/\s+/g, '');

    const body = { bodyHtml, footerHtml };
    const params = { pdfType: `document:${documentType}` as IDownloadDocumentQueryParam };

    const buffer = await UtilityAPIS.downloadDocument(body, params);
    if (!buffer) return;
    return buffer;
  }

  public async convertDocumentToPDFAndSendEmail(
    res: Response,
    data: ISendPDFDocumentData,
    email: string,
    user: IAuthenticatedUser,
    template_layout: 'no-logo' | 'main' | 'payment',
    userTimezone: string
  ) {
    data.pdf = true;
    const type = trimAndLowerCase(data.documentType);
    const template: string = getTemplateName(type, data);
    const pdfBuffer = await this.convertDocumentToPDF(data);
    const company = removeWhiteSpace(data.businessName);
    const name: string = `${company}-${data.documentType}`;
    const filename: string = getFileName(FILE_EXTENSION.PDF, name);

    const templateData: ISendPDFTemplateData = {
      template,
      customerName: data.customerName,
      firstname: user.firstname,
      reason: data.reason,
      reference: data.reference,
      paymentMethod: data.paymentMethod,
      businessName: data.businessName,
      documentNumber: data.documentNumber,
      invoiceNumber: data.invoiceNumber,
      totalAmount: data.totalAmount,
      paymentLink: data.paymentLink,
      amountPaid: data.amountPaid,
      dateIssued: data.dateIssued,
      datePaid: formatDateToYearMonthDayTime(data.datePaid, userTimezone),
      currency: data.currency,
      expiryDate: formatDateToYearMonthDayTime(data.expiryDate, userTimezone),
    };

    const html = await renderTemplate(res, templateData, template_layout);
    const docType = type === DOCUMENT_TYPE.CREDIT_NOTE ? 'Credit Note' : type;
    const mailOptions = {
      email,
      subject: data.subject
        ? data.subject
        : `You have received a new ${docType} from ${data.businessName}.`,
      html,
      systemEmail: process.env.NO_REPLY_EMAIL_USERNAME,
      attachments: {
        filename: `${filename}`,
        content: pdfBuffer,
      },
    };
    if (!data.pdf) delete mailOptions.attachments;
    await EmailFeatures.sendEmail(mailOptions);
    if (type === DOCUMENT_TYPE.INVOICE) {
      await EmailFeatures.invoiceSentMail(data, user, res);
    }
    if (type === DOCUMENT_TYPE.RECEIPT) {
      await EmailFeatures.receiptSentMail(data, user, res);
    }
    if (type === DOCUMENT_TYPE.CREDIT_NOTE) {
      await EmailFeatures.creditNoteSentMail(data, user, res);
    }
    return true;
  }

  async sendInvoiceReminder(
    res: Response,
    user: IAuthenticatedUser,
    userTimeZone: string,
    payload: SendInvoiceReminderPayload
  ) {
    const filter: WhereOptions<DocumentAttributes> = {
      orgId: user.organization.id,
      documentNumber: payload.documentNumber,
      type: DOCUMENT_TYPE.INVOICE,
    };

    const options = getReminderDocumentOptions();
    const document = await this.getOne(filter, options, true);

    if ([DOCUMENT_STATUS.DRAFT, DOCUMENT_STATUS.COMPLETED].includes(document.status)) {
      throw new ConflictError(Errors.INVALID_INVOICE_FOR_REMINDER);
    }

    const { organization } = user;

    let fullDetails = getReminderDocumentFullDetails(organization, userTimeZone, document);

    fullDetails = formatDocumentFiguresWithComma(fullDetails);

    const template = EMAIL_TEMPLATES.invoicePayment;
    const pdfBuffer = await this.convertDocumentToPDF(fullDetails);

    let filename = `${fullDetails.businessName}-${fullDetails.documentType}`;
    filename = getFileName(FILE_EXTENSION.PDF, filename);

    const templateData = getReminderInvoiceTemplateData(template, user.firstname, {
      ...fullDetails,
    });

    const html = await renderTemplate(res, templateData, 'no-logo');
    const mailData = {
      email: payload.recipientEmail,
      subject: EMAIL_SUBJECTS.invoiceReminder,
      systemEmail: process.env.NO_REPLY_EMAIL_USERNAME,
      html,
      attachments: {
        filename: `${filename}`,
        content: pdfBuffer,
      },
    };

    await EmailFeatures.sendEmail(mailData);
    return true;
  }
}
