import express from 'express';
import container from '../../../containers/container.global';
import {
  validateCreateCreditNote,
  validateCreateInvoice,
  validateCreateReceipt,
  validateDownloadDocumentById,
  validateSendDocumentById,
  validateUpdateCreditNote,
  validateUpdateInvoice,
} from '../../../middlewares/validation/documents.validators';
import {
  populateBusinessDetails,
  processDocumentDataByDocumentNumber,
} from '../../../middlewares/utils/utils.middleware';
import { USERAPP_GLOBAL_ACCESS } from '../../../constants/values.constants';
import * as validation from '../../../middlewares/validation/global.validators';
import { uuidParamSchema } from '../../../middlewares/validation/helpers.utils';
import { sendInvoiceReminderSchema } from '../../../middlewares/validation/schemas/request-body/document.schemas';

const documentRoutes = express.Router();
const authController = container.resolve('authMiddleware');
const documentControllers = container.resolve('documentControllers');

documentRoutes.get(
  '/download',
  validateDownloadDocumentById,
  processDocumentDataByDocumentNumber,
  documentControllers.downloadDocument.bind(documentControllers)
);

documentRoutes.get('/filter', documentControllers.filterDocument.bind(documentControllers));

documentRoutes.get('/archives', documentControllers.getArchiveDocuments.bind(documentControllers));

documentRoutes.get('/recent', documentControllers.getRecentDocuments.bind(documentControllers));

documentRoutes.get('/search', documentControllers.searchDocument.bind(documentControllers));

documentRoutes.get(
  '/:id',
  validation.validateParamsBody(uuidParamSchema),
  documentControllers.getDocument.bind(documentControllers)
);

documentRoutes.get('/', documentControllers.getDocuments.bind(documentControllers));

documentRoutes.use(authController.authRestrictTo(USERAPP_GLOBAL_ACCESS).bind(authController));

documentRoutes.post(
  '/send',
  validateSendDocumentById,
  processDocumentDataByDocumentNumber,
  documentControllers.sendPdfDocumentToEmail.bind(documentControllers)
);

documentRoutes.post(
  '/send-reminder',
  validation.validateRequestBody(sendInvoiceReminderSchema),
  documentControllers.sendInvoiceReminder.bind(documentControllers)
);

// documentRoutes.post(
//   '/send-reminder',
//   validateInvoiceReminder,
//   populateReminderDetails,
//   documentControllers.sendReminder.bind(documentControllers)
// );

documentRoutes.post(
  '/receipt',
  validateCreateReceipt,
  documentControllers.createReceipt.bind(documentControllers)
);

documentRoutes.post(
  '/invoice',
  validateCreateInvoice,
  populateBusinessDetails,
  documentControllers.createInvoice.bind(documentControllers)
);

documentRoutes.post(
  '/creditnote',
  validateCreateCreditNote,
  documentControllers.createCreditNote.bind(documentControllers)
);

documentRoutes.patch(
  '/invoice/:id',
  validation.validateParamsBody(uuidParamSchema),
  validateUpdateInvoice,
  populateBusinessDetails,
  documentControllers.updateDocument.bind(documentControllers)
);

documentRoutes.patch(
  '/creditnote/:id',
  validation.validateParamsBody(uuidParamSchema),
  validateUpdateCreditNote,
  documentControllers.updateDocument.bind(documentControllers)
);

documentRoutes.patch('/:id/archive', documentControllers.archiveDocument.bind(documentControllers));

documentRoutes.patch(
  '/:id/unarchive',
  documentControllers.unArchiveDocument.bind(documentControllers)
);

documentRoutes.delete('/:id', documentControllers.deleteDocument.bind(documentControllers));

export default documentRoutes;
