import { Router } from 'express';
import statRouter from './stats.routes';
import invoiceRouter from './invoice.routes';
import customerRouter from './customer.routes';
import itemRouter from './item.routes';
import serviceRouter from './service.routes';
import container from '../../containers/container.global';
import { extractOrganizationDetailsFromRequest } from '../../middlewares/utils/utils.middleware';
import documentRouter from './document.routes';
import * as validation from '../../middlewares/validation/global.validators';
import Joi from 'joi';
import { isProductionEnv } from '../../utilities/guards';
import { RateLimiters } from '../../middlewares/utils/rate-limiter.middleware';

const AdminAccessRouter = Router();
const Auth = container.resolve('authMiddleware');

if (isProductionEnv) {
  AdminAccessRouter.use(RateLimiters.adminRequest);
}

AdminAccessRouter.use(Auth.authenticateUser.bind(Auth));
AdminAccessRouter.use(Auth.authRestrictToAdminAccess.bind(Auth));

AdminAccessRouter.use(
  validation.validateParamsBody(
    Joi.object({
      organizationId: Joi.string()
        .regex(/^[a-zA-Z0-9-]{3,50}$/)
        .required(),
    })
  )
);

AdminAccessRouter.use(extractOrganizationDetailsFromRequest);

AdminAccessRouter.use([`/`], documentRouter);
AdminAccessRouter.use([`/customers`], customerRouter);
AdminAccessRouter.use([`/invoices`], invoiceRouter);
AdminAccessRouter.use([`/items`], itemRouter);
AdminAccessRouter.use([`/services`], serviceRouter);
AdminAccessRouter.use([`/stats`], statRouter);

export default AdminAccessRouter;
