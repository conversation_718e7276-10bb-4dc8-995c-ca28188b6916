import express from 'express';
import container from '../../containers/container.global';
import {
  validateCreateCreditNote,
  validateCreateInvoice,
  validateCreateReceipt,
  validateDownloadDocumentById,
  validateSendDocument,
  validateUpdateCreditNote,
  validateUpdateInvoice,
} from '../../middlewares/validation/documents.validators';
import {
  populateBusinessDetails,
  processDocumentDataByDocumentNumber,
} from '../../middlewares/utils/utils.middleware';

const documentRouter = express.Router();
const documentControllers = container.resolve('documentControllers');

documentRouter.get('/', documentControllers.getAllDocuments.bind(documentControllers));

// documentRouter.get('/org', documentControllers.getAllDocuments.bind(documentControllers));

// TODO: add route to get one archived document.

documentRouter.get('/filter', documentControllers.filterDocument.bind(documentControllers));

documentRouter.get('/search', documentControllers.searchDocument.bind(documentControllers));

documentRouter.get('/archives', documentControllers.getArchiveDocuments.bind(documentControllers));

documentRouter.get(
  '/download',
  validateDownloadDocumentById,
  processDocumentDataByDocumentNumber,
  documentControllers.downloadDocument.bind(documentControllers)
);

documentRouter.post(
  '/send',
  validateSendDocument,
  documentControllers.sendPdfDocumentToEmail.bind(documentControllers)
);

// documentRouter.post(
//   '/send-reminder',
//   validateInvoiceReminder,
//   populateReminderDetails,
//   documentControllers.sendReminder.bind(documentControllers)
// );

documentRouter.get('/:id', documentControllers.getDocument.bind(documentControllers));

documentRouter.post(
  '/receipt',
  validateCreateReceipt,
  documentControllers.createReceipt.bind(documentControllers)
);

documentRouter.post(
  '/invoice',
  validateCreateInvoice,
  populateBusinessDetails,
  documentControllers.createInvoice.bind(documentControllers)
);

documentRouter.post(
  '/creditnote',
  validateCreateCreditNote,
  documentControllers.createCreditNote.bind(documentControllers)
);

documentRouter.patch(
  '/:id/edit-invoice',
  validateUpdateInvoice,
  populateBusinessDetails,
  documentControllers.updateDocument.bind(documentControllers)
);

documentRouter.patch(
  '/:id/edit-creditnote',
  validateUpdateCreditNote,
  documentControllers.updateDocument.bind(documentControllers)
);

documentRouter.patch('/:id/archive', documentControllers.archiveDocument.bind(documentControllers));

documentRouter.patch(
  '/:id/unarchive',
  documentControllers.unArchiveDocument.bind(documentControllers)
);

documentRouter.delete('/:id', documentControllers.deleteDocument.bind(documentControllers));

export default documentRouter;
