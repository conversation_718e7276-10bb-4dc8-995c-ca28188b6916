import { NotificationCollection } from '../constants/notification.constants';

export type FileExtensionType = {
  PDF: string;
  JPEG: string;
  JPG: string;
  PNG: string;
};

export type NotificationMessageType = {
  getNotifications: string;
  createNotifícation: string;
  markNotificationRead: string;
  markAllNotificationRead: string;
  createInvoice: string;
  createReceipt: string;
  createCreditNote: string;
  sendDocument: string;
  adminCreateInvoice: string;
  adminCreateReceipt: string;
  adminCreateCreditNote: string;
  adminUpdateDocument: string;
  adminDeleteDocument: string;
  adminSendDocument: string;
  adminSendReminder: string;
  adminArchiveDocument: string;
  adminUnArchivedDocument: string;
  adminChangeTeamRole: string;
  adminRemoveDevice: string;
};

export type DateRangeMapType = {
  [key: string]: [Date, Date];
};

export type TimeIntervalType = {
  startDate: Date;
  endDate: Date;
  grouping: string;
};

export type DateOptionsType = {
  day: string;
  month: string;
  year: string;
  hour: string;
  minute: string;
  hour12: boolean;
};

export type ValidQueues =
  | 'saveRequestLogsQueue'
  | 'sendEmailNotificationQueue'
  | 'appNotificationQueue';

type NotificationType = 'USER' | 'ORGANISATION';

type TaskPriority = 'LOW' | 'MEDIUM' | 'HIGH';

export interface NotificationAttributes {
  user_ids?: string[];
  org_id: string;
  title: string;
  user_id?: string;
  message: string;
  emit_event?: string;
  type: NotificationType;
  priority: TaskPriority;
  event_name: string;
  collection: NotificationCollection;
  exclude_users?: Array<string>;
}
