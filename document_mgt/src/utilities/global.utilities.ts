import { Request, Response } from 'express';
import FormData from 'form-data';
import RequestIP from 'request-ip';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import {
  BASE_10,
  DOCUMENT_TYPE,
  FILE_EXTENSION,
  HUNDRED,
  NOTIFICATION_EVENT_NAMES,
} from '../constants/values.constants';
import geoip from 'geoip-lite';
import { toZonedTime } from 'date-fns-tz';
import {
  isDevelopmentEnv,
  isProductEntity,
  isProductionEnv,
  isServiceEntity,
  isTestENV,
} from './guards';
import { ROLES } from '../models/enums';
import { EMAIL_TEMPLATES } from '../constants/email.constants';
import { ISendPDFDocumentData } from '../interfaces/documents.interfaces';
import { catchError } from './catch-async-error';
import { EmailFormOptions } from '../interfaces/email.interfaces';
import { BadRequestError, InternalServerError } from '../middlewares/error_handlers/app-error';
import { NotificationAttributes, TaskPriority } from '../types';
import { OrganizationMember } from '../interfaces/user.interfaces';
import { NOTIFICATION_COLLECTIONS } from '../constants/notification.constants';
import container from '../containers/container.global';

export const getUserAgentHeader = (req: Request) => req.headers['user-agent'];

export const generateToken = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

export const genReference = () => {
  const reference = uuidv4().slice(0, 12).split('-').join('');
  return reference;
};

export const getUserTimeZone = (req: Request): string => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'UTC';
  return userTimezone;
};

export const formatDateToYearMonthDayTime = (dateValue: Date, userTimezone: string) => {
  if (!dateValue) return null;
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true, // Enables AM/PM format
    timeZone: userTimezone, // Ensures consistent time formatting
  };

  const formattedDate = new Date(dateValue).toLocaleString('en-GB', options).replace(',', '');
  if (formattedDate === 'Invalid Date') return null;
  return formattedDate;
};

export const formatDateToDayMonthYear = (dateValue: Date, userTimezone: string) => {
  if (!dateValue) return null;

  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    timeZone: userTimezone,
  };

  const formattedDate = new Date(dateValue).toLocaleDateString('en-GB', options);
  if (formattedDate === 'Invalid Date') return null;

  return formattedDate; // e.g., "11 Apr 2025"
};

export const renderTemplate = (
  res: Response,
  data: Record<string, any>,
  template_layout: 'main' | 'no-logo' | 'payment'
): Promise<string> => {
  let layout: string;
  if (template_layout === 'main') layout = 'main';
  if (template_layout === 'no-logo') layout = 'no-logo';
  if (template_layout === 'payment') layout = 'payment';
  return new Promise<string>((resolve) => {
    res.render(`${data.template}`, { layout, data, cacheBuster: Date.now() }, (err, html) => {
      if (err) {
        console.error('Error rendering template:', err);
        throw new InternalServerError();
      } else {
        resolve(html);
      }
    });
  });
};

export const pagination = (req: Request): { offset: number; limit: number; page: number } => {
  const { offset: page = 1, limit = 50 } = req.query;
  if (parseInt(page as unknown as string, BASE_10) < 1)
    throw new BadRequestError('offset must be greater than 0.');
  let parsedPerPage: number = typeof limit === 'string' ? parseInt(limit) : (limit as number);
  if (isNaN(parsedPerPage)) {
    parsedPerPage = 50;
  }

  let parsedPage: number = typeof page === 'string' ? parseInt(page) : (page as number);
  if (isNaN(parsedPage)) {
    parsedPage = 1;
  }
  const paginate = {
    offset: (parsedPage - 1) * parsedPerPage,
    limit: parsedPerPage,
    page: parsedPage,
  };

  return paginate;
};

export function getPagination(req: Request): { page: number; offset: number; limit: number } {
  const defaultPage = 1;
  const defaultLimit = 50;
  const maxPaginationLimit = 100;

  const rawPage = req.query.page;
  const rawLimit = req.query.limit;

  const page = parseInt(typeof rawPage === 'string' ? rawPage : '', BASE_10);
  const limit = parseInt(typeof rawLimit === 'string' ? rawLimit : '', BASE_10);

  const isValidPage = Number.isInteger(page) && page > 0;
  const isValidLimit = Number.isInteger(limit) && limit > 0;

  const safeLimit = isValidLimit ? Math.min(limit, maxPaginationLimit) : defaultLimit;
  const safePage = isValidPage ? page : defaultPage;
  const offset = (safePage - 1) * safeLimit;

  return {
    page: safePage,
    limit: safeLimit,
    offset,
  };
}

export const getFileName = (filetype: string, filename: string): string | undefined => {
  if (filetype === 'pdf') return filename + '.' + FILE_EXTENSION.PDF;
};

export const getFileNameWithExtension = (filename: string, extension: string): string => {
  return `${filename}.${extension}`;
};

export const removeWhiteSpace = (word: string): string | undefined => {
  if (!word) return;
  return word.replace(/\s+/g, '');
};

export const trimAndLowerCase = (word: string): string => {
  if (!word || word === '') return '';
  return word.trim().toLowerCase();
};

export const getPublicAddress = (req: Request): string => {
  const xForwardedFor = req.header('x-forwarded-for');
  const ip =
    (xForwardedFor ? xForwardedFor.split(',')[0].trim() : null) ||
    RequestIP.getClientIp(req) ||
    req.ips[0] ||
    req.ip;
  if (!ip) return '';
  if (ip === '::1' || ip === '127.0.0.1' || ip.startsWith('::ffff:')) {
    return 'localhost';
  }
  return ip;
};

export const convertHexToRgba = (hex: string, opacity: number) => {
  if (hex && opacity) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return '';
};

export const createImagesFolder = () => {
  const folderName: string = isTestENV ? 'local' : 'public';
  const folderPath = path.join(__dirname, `../../${folderName}`);
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath);
    const imagesFolderPath = path.join(folderPath, 'images');
    fs.mkdirSync(imagesFolderPath);
  }
};

export const currentTimestamp = (req: Request) => {
  const ip = req.ip || '0.0.0.0'; // Replace with a default IP if needed
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'UTC';
  const utcDate = new Date(); // Current time in UTC
  const zonedDate = toZonedTime(utcDate, userTimezone);
  return zonedDate;
};

export const transformUserBusinessFields = (user: any | any[]): any | any[] => {
  const transformBusinessFields = (user) => {
    if (!user || !user.Business) return user;

    const business = user.Business;
    const transformedBusinessFields = Object.entries(business).reduce((acc, [key, value]) => {
      const camelKey = key.charAt(0).toLowerCase() + key.slice(1); // Ensure camelCase
      const prefixedKey = `business${camelKey.charAt(0).toUpperCase()}${camelKey.slice(1)}`;
      acc[prefixedKey] = value;
      return acc;
    }, {});

    delete user.Business;
    return { ...user, ...transformedBusinessFields };
  };

  if (Array.isArray(user)) {
    return user.map(transformBusinessFields);
  }
  return transformBusinessFields(user);
};

export const WEB_EVENTS_URL =
  isProductionEnv || isDevelopmentEnv
    ? process.env.WEB_EVENT_URL
    : 'http://127.0.0.1:4000/webevents/notification';
export const BASE_URL = isProductionEnv ? process.env.SERVER_PROD_URL : process.env.SERVER_DEV_URL;

export const LOCALHOST = `http:127.0.0.1:${process.env.PORT || 8752}`;
export const GOOGLE_API_BASE_URL = 'https://www.googleapis.com';
export const GOOGLE_ACCOUNT_BASE_URL = 'https://accounts.google.com/o/oauth2/v2/auth';

export const ROLES_ARRAY = Object.values(ROLES);

export const getUpdatedFields = <T extends object>(
  existingData: T,
  updatedData: Partial<T>
): Partial<T> =>
  Object.fromEntries(
    Object.entries(updatedData).filter(([key, value]) => existingData[key as keyof T] !== value)
  ) as Partial<T>;

export const getTemplateName = (docType: string, data?: ISendPDFDocumentData) => {
  let template: string;
  if (docType === DOCUMENT_TYPE.INVOICE)
    template = data.template || EMAIL_TEMPLATES.invoiceCreation;
  if (docType === DOCUMENT_TYPE.RECEIPT)
    template = data.template || EMAIL_TEMPLATES.receiptIssuance;
  if (docType === DOCUMENT_TYPE.CREDIT_NOTE)
    template = data.template || EMAIL_TEMPLATES.creditNoteIssuance;
  return template;
};

export const generateEmailForm = catchError((options: EmailFormOptions) => {
  const { to, subject, html, attachments, from } = options;

  const form = new FormData();

  form.append('from', from);
  form.append('to', to);
  form.append('subject', subject);
  form.append('html', html);
  if (attachments && attachments.length > 0) {
    attachments.forEach((attachment) => {
      form.append('attachments', attachment.buffer, { filename: attachment.filename });
    });
  }

  return form;
});

export const extractIDocumentNumber = (documentNumber: string): string => {
  if (!documentNumber) return;
  const splittedDocumentNum = documentNumber.split('-');
  return `${splittedDocumentNum[0].toUpperCase()}-${splittedDocumentNum[1]}`;
};

export const formatNumberWithCommas = (input: number | string) => {
  if (input === null || input === undefined || input === '' || input === 0) return '0.00';

  const cleanedInput = String(input).trim().replace(/,/g, '');

  const parsedNumber = Number(cleanedInput);

  if (isNaN(parsedNumber)) return '0.00';
  const fixedNumber = truncateToTwoDecimals(parsedNumber);
  const stringifyNumber = String(fixedNumber);

  const parts = stringifyNumber.split('.');
  let decimalPart: string;
  const integerPart = parts[0];
  if (isTwoDigitString(parts[1])) decimalPart = parts[1];
  else if (isOneDigitString(parts[1])) decimalPart = `${parts[1]}0`;
  else decimalPart = '00';

  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  const formattedNumber = `${formattedInteger}.${decimalPart}`;

  return parsedNumber < 0 && !formattedNumber.startsWith('-')
    ? `-${formattedNumber}`
    : formattedNumber;
};

export const isTwoDigitString = (str: string) => {
  return /^[0-9]{2}$/.test(str);
};

export const isOneDigitString = (str: string) => {
  return /^[0-9]{1}$/.test(str);
};

export const calcSubTotalAmount = (amounts) => {
  const initialValue = 0;
  const subTotalAmounts = amounts.reduce(
    (accumulator, currentValue) => accumulator + currentValue,
    initialValue
  );
  return truncateToTwoDecimals(subTotalAmounts);
};

export const calcTotalAmount = (amount, totalVat, totalDiscount) => {
  let totalAmount = amount;
  totalAmount -= totalDiscount;
  totalAmount += totalVat;
  return truncateToTwoDecimals(totalAmount);
};

export const calculateVAT = (amount, rate) => {
  const result = (amount * rate) / HUNDRED;
  return result;
};

export const calculateDiscount = (amount, discount) => {
  const result = (amount * discount) / HUNDRED;
  return result;
};

export const calcEntityAmountAndGetAmounts = (data, entityType) => {
  const entityData = [];
  const amounts = [];
  const totalFees = [];
  let totalVat = 0;
  let totalDiscount = 0;
  if (isProductEntity(entityType)) {
    data.items.map((entity) => {
      entity.amount = parseFloat(entity.unitPrice) * parseFloat(entity.quantity);
      const discount: number = calculateDiscount(entity.amount, entity.discount);
      const vat: number = calculateVAT(entity.amount - discount, entity.vat);
      totalVat += vat;
      totalDiscount += discount;
      amounts.push(entity.amount);
      entityData.push(entity);
    });
  } else if (isServiceEntity(entityType)) {
    data.services.map((entity) => {
      if (entity.hours && entity.hourlyRate) {
        entity.totalFee = entity.hourlyRate * entity.hours;
      }
      const discount: number = calculateDiscount(entity.totalFee, entity.discount);
      const vat: number = calculateVAT(entity.totalFee - discount, entity.vat);
      totalVat += vat;
      totalDiscount += discount;
      totalFees.push(entity.totalFee);
      entityData.push(entity);
    });
  }
  return { entityData, amounts, totalFees, totalVat, totalDiscount };
};

export const formatAllEntities = (entities, entityType) => {
  return entities.map((entity) => {
    if (isProductEntity(entityType)) {
      return {
        vat: entity.vat,
        name: entity.name,
        quantity: entity.quantity,
        discount: entity.discount,
        description: entity.description,
        amount: formatNumberWithCommas(entity.amount),
        unitPrice: formatNumberWithCommas(entity.unitPrice),
      };
    }

    if (isServiceEntity(entityType)) {
      return {
        vat: entity.vat,
        type: entity.type,
        hours: entity.hours,
        discount: entity.discount,
        description: entity.description,
        totalFee: formatNumberWithCommas(entity.totalFee),
        hourlyRate: formatNumberWithCommas(entity.hourlyRate),
      };
    }
  });
};

export const truncateToTwoDecimals = (num: number | null | undefined): number => {
  if (num == null || isNaN(num)) return 0;

  const parts = num.toString().split('.');
  if (parts.length === 1) return num; // No decimal part

  const truncatedDecimal = parts[1].slice(0, 2).padEnd(2, '0');
  return Number(`${parts[0]}.${truncatedDecimal}`);
};

export const incrementDocumentId = (prefix: string, str: string): Promise<string> => {
  return new Promise<string>((resolve) => {
    const regex = new RegExp(`(${prefix}-)(\\d+)`, 'g');

    function replacer(match: string, prefix: string, number: string): string {
      const incrementedNumber = parseInt(number, BASE_10) + 1;
      return prefix + String(incrementedNumber).padStart(number.length, '0');
    }

    const result = str.replace(regex, replacer);
    resolve(result);
  });
};

export const calcItemAmountAndGetAmounts = (data: any[]): any => {
  const itemData: any = [];
  const amounts: number[] = [];
  let totalVat: number = 0;
  let totalDiscount: number = 0;
  data.map((item: Record<string, any>) => {
    item.amount = parseFloat(item.unitPrice) * parseFloat(item.quantity);
    const discount = calculateDiscount(item.amount, item.discount);
    const vat = calculateVAT(item.amount - discount, item.vat);
    totalVat += vat;
    totalDiscount += discount;
    amounts.push(item.amount);
    itemData.push(item);
  });
  totalDiscount = truncateToTwoDecimals(totalDiscount);
  totalVat = truncateToTwoDecimals(totalVat);
  return { itemData, amounts, totalVat, totalDiscount };
};

export const calcServiceAmountAndGetAmounts = (data: any[]): any => {
  const serviceData: any = [];
  const amounts: number[] = [];
  let totalVat: number = 0;
  let totalDiscount: number = 0;
  data.map((service: Record<string, any>) => {
    if (service.hourlyRate && service.hours) {
      service.totalFee = parseFloat(service.hourlyRate) * parseFloat(service.hours);
    } else {
      service.totalFee = parseFloat(service.totalFee);
    }
    const discount = calculateDiscount(service.totalFee, service.discount);
    const vat = calculateVAT(service.totalFee - discount, service.vat);
    totalVat += vat;
    totalDiscount += discount;
    service.totalFee = truncateToTwoDecimals(service.totalFee);
    amounts.push(service.totalFee);
    serviceData.push(service);
  });
  totalDiscount = truncateToTwoDecimals(totalDiscount);
  totalVat = truncateToTwoDecimals(totalVat);
  return { serviceData, amounts, totalVat, totalDiscount };
};

// process the notification data
function processUserNotificationData(
  title: string,
  message: string,
  userId: string,
  orgId: string,
  userIds: string[],
  priority: TaskPriority = 'MEDIUM',
  excludedUsers: string[] = [],
  collection: NotificationAttributes['collection'] = NOTIFICATION_COLLECTIONS.DOCUMENTS
): NotificationAttributes {
  return {
    user_id: userId,
    org_id: orgId,
    user_ids: userIds,
    title,
    message,
    type: 'ORGANISATION',
    priority,
    event_name: NOTIFICATION_EVENT_NAMES.app,
    collection,
    exclude_users: excludedUsers,
  };
}

// send the notification data to the notification queue for processing
export async function sendOrganizationNotification(
  res: Response,
  title: string,
  message: string,
  priority: TaskPriority = 'MEDIUM',
  excludedUsers = [],
  collection: NotificationAttributes['collection'] = NOTIFICATION_COLLECTIONS.DOCUMENTS
) {
  const userId = res.locals.userInfo.id;
  const orgId = res.locals.orgId as string;

  const orgMembers = res.locals.orgMembers as OrganizationMember[];
  const orgMembersUserIds = orgMembers.map((member) => member.userId);
  const uniqueUserIds = [...new Set(orgMembersUserIds)];
  const filteredUserIds = uniqueUserIds.filter((userId) => !excludedUsers.includes(userId));

  const data = processUserNotificationData(
    title,
    message,
    userId,
    orgId,
    filteredUserIds,
    priority,
    excludedUsers,
    collection
  );
  if (excludedUsers.length === 0) delete data.exclude_users;

  const taskManager = container.resolve('backgroundTaskManagers');
  return await taskManager.queueTasks(data, 'appNotificationQueue');
}
