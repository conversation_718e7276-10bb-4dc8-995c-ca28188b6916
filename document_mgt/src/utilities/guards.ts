import {
  ALLOWED_ARCHIVES,
  DOCUMENT_ENTITY_TYPE,
  DOCUMENT_ENTITY_TYPE_ARRAY,
  DOCUMENT_TYPE_ARRAY,
} from '../constants/values.constants';
import { ROLES } from '../models/enums';

export const isProductionEnv = process.env.NODE_ENV === 'production';
export const isDevelopmentEnv = process.env.NODE_ENV === 'development';
export const isTestENV = process.env.NODE_ENV === 'test';

export const isSuperAdmin = (role: string): boolean => {
  let val: boolean;
  role === ROLES.SUPERADMIN ? (val = true) : (val = false);
  return val;
};

export const isValuePresent = (value: any): boolean => {
  return value !== '' && value !== null && value !== undefined;
};

export const is2FAenabled = (authenticator: boolean): boolean => {
  if (!authenticator) return false;
  return true;
};

export const isNotAcceptedDocument = (document: string, documentArray: string[]): boolean => {
  const documentValue = document.trim().toLowerCase();
  return !documentArray.some((doc) => doc.trim() === documentValue);
};

export const isNotAcceptedStatus = (status: string, statusArray: string[]): boolean => {
  const statusValue = status.trim().toLowerCase();
  return !statusArray.some((doc) => doc.trim() === statusValue);
};

export const isNotAcceptedEntity = (entity: string, entityArray: string[]): boolean => {
  const entityValue = entity.trim().toLowerCase();
  return !entityArray.some((doc) => doc.trim() === entityValue);
};

export const isAllowedArchiveDocument = (status: string): boolean => {
  if (ALLOWED_ARCHIVES.includes(status)) return true;
};
export const isAllowedDocument = (document: string): boolean => {
  if (DOCUMENT_TYPE_ARRAY.includes(document)) return true;
};
export const isAllowedDocumentEntity = (document: string): boolean => {
  if (DOCUMENT_ENTITY_TYPE_ARRAY.includes(document)) return true;
};

export const isProductEntity = (entity: string): boolean => {
  if (entity === DOCUMENT_ENTITY_TYPE.PRODUCT) return true;
};

export const isServiceEntity = (entity: string): boolean => {
  if (entity === DOCUMENT_ENTITY_TYPE.SERVICE) return true;
};
