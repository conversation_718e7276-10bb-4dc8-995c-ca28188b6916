import { ContainerKeys } from './container.types';
import CustomerControllers from '../controllers/customer.controller';
import DocumentController from '../controllers/documents/documents.controller';
import InvoiceController from '../controllers/documents/invoice.controller';
import ItemControllers from '../controllers/item.controller';
import StatControllers from '../controllers/stats.controller';
import CustomerServices from '../services/customers/customer.services';
import ItemServices from '../services/items/items.services';
import StatServices from '../services/stats/stats.services';
import CreditNoteServices from '../services/documents/credit-note.services';
import DocumentServices from '../services/documents/documents.services';
import InvoiceServices from '../services/documents/invoice.services';
import ReceiptServices from '../services/documents/receipt.services';
import ServiceServices from '../services/service/service.services';
import { ContainerInstanceTypes } from './container.interfaces';
import ServiceControllers from '../controllers/service.controller';
import { AuthMiddleware } from '../middlewares/auth/auth.middleware';
import UtilitiesControllers from '../controllers/utilities.controller';
import productServices from '../services/product.service';
import ProductControllers from '../controllers/product.controller';
import ImageUploader from '../middlewares/utils/image-upload.utilities';
import ProductCategoryServices from '../services/category.service';
import ProductCategoryControllers from '../controllers/product-category.controller';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';

class Container {
  private instances: Partial<Record<ContainerKeys, ContainerInstanceTypes[ContainerKeys]>> = {};

  register<K extends ContainerKeys>(key: K, instance: ContainerInstanceTypes[K]): void {
    this.instances[key] = instance;
  }

  resolve<K extends ContainerKeys>(key: K): ContainerInstanceTypes[K] {
    return this.instances[key] as ContainerInstanceTypes[K];
  }
}

const container = new Container();

container.register('utilitiesControllers', new UtilitiesControllers());
container.register('authMiddleware', new AuthMiddleware());
container.register('receiptServices', new ReceiptServices());
container.register('creditNoteServices', new CreditNoteServices());

container.register('customerServices', new CustomerServices());
container.register('itemServices', new ItemServices());
container.register('serviceServices', new ServiceServices());

container.register(
  'invoiceServices',
  new InvoiceServices(
    container.resolve('itemServices'),
    container.resolve('serviceServices'),
    container.resolve('customerServices')
  )
);

container.register(
  'documentService',
  new DocumentServices(
    container.resolve('invoiceServices'),
    container.resolve('receiptServices'),
    container.resolve('creditNoteServices'),
    container.resolve('itemServices'),
    container.resolve('customerServices'),
    container.resolve('serviceServices')
  )
);

container.register('statServices', new StatServices());

container.register(
  'documentControllers',
  new DocumentController(
    container.resolve('documentService'),
    container.resolve('itemServices'),
    container.resolve('serviceServices'),
    container.resolve('customerServices')
  )
);

container.register(
  'invoiceControllers',
  new InvoiceController(container.resolve('invoiceServices'))
);

container.register(
  'customerControllers',
  new CustomerControllers(container.resolve('customerServices'))
);

container.register('itemControllers', new ItemControllers(container.resolve('itemServices')));
container.register(
  'serviceControllers',
  new ServiceControllers(container.resolve('serviceServices'))
);
container.register('statControllers', new StatControllers(container.resolve('statServices')));

container.register('productCategoryServices', new ProductCategoryServices());

container.register(
  'productServices',
  new productServices(container.resolve('productCategoryServices'))
);
container.register(
  'productControllers',
  new ProductControllers(container.resolve('productServices'))
);

container.register(
  'productCategoryControllers',
  new ProductCategoryControllers(container.resolve('productCategoryServices'))
);

container.register('imageUploader', new ImageUploader());

container.register('backgroundTaskManagers', new BackgroundTaskManager());

export default container;
