import CustomerControllers from '../controllers/customer.controller';
import DocumentController from '../controllers/documents/documents.controller';
import InvoiceController from '../controllers/documents/invoice.controller';
import ProductControllers from '../controllers/product.controller';
import ItemControllers from '../controllers/item.controller';
import ServiceControllers from '../controllers/service.controller';
import StatControllers from '../controllers/stats.controller';
import UtilitiesControllers from '../controllers/utilities.controller';
import { AuthMiddleware } from '../middlewares/auth/auth.middleware';
import ImageUploader from '../middlewares/utils/image-upload.utilities';
import CustomerServices from '../services/customers/customer.services';
import CreditNoteServices from '../services/documents/credit-note.services';
import DocumentServices from '../services/documents/documents.services';
import InvoiceServices from '../services/documents/invoice.services';
import ReceiptServices from '../services/documents/receipt.services';
import productServices from '../services/product.service';
import ItemServices from '../services/items/items.services';
import ServiceServices from '../services/service/service.services';
import StatServices from '../services/stats/stats.services';
import ProductCategoryServices from '../services/category.service';
import ProductCategoryControllers from '../controllers/product-category.controller';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';

export interface ContainerInstanceTypes {
  receiptServices: ReceiptServices;
  creditNoteServices: CreditNoteServices;
  customerServices: CustomerServices;
  itemServices: ItemServices;
  serviceServices: ServiceServices;
  invoiceServices: InvoiceServices;
  documentService: DocumentServices;
  statServices: StatServices;
  documentControllers: DocumentController;
  invoiceControllers: InvoiceController;
  customerControllers: CustomerControllers;
  itemControllers: ItemControllers;
  serviceControllers: ServiceControllers;
  statControllers: StatControllers;
  authMiddleware: AuthMiddleware;
  utilitiesControllers: UtilitiesControllers;
  productServices: productServices;
  productControllers: ProductControllers;
  productCategoryServices: ProductCategoryServices;
  productCategoryControllers: ProductCategoryControllers;
  imageUploader: ImageUploader;
  backgroundTaskManagers: BackgroundTaskManager;
}
