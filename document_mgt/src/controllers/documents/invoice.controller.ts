import { Request, Response } from 'express';
import InvoiceServices from '../../services/documents/invoice.services';
import { getResponse, ResponseMeta } from '../../utilities/responses.utilities';
import { DOCUMENT_MESSAGES } from '../../constants/responses.constants';
import {
  extractIDocumentNumber,
  pagination,
  trimAndLowerCase,
} from '../../utilities/global.utilities';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { USER_ACTIONS } from '../../constants/values.constants';
export default class InvoiceController extends RequestHandlerErrorWrapper {
  constructor(private readonly invoiceServices: InvoiceServices) {
    super();
  }

  public async getInvoices(req: Request, res: Response) {
    const { orgId } = res.locals;
    const paginate = pagination(req);
    if (!paginate) return;

    const responseData = await this.invoiceServices.getInvoices(orgId, paginate);

    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      page: paginate.page,
      limit: paginate.limit,
      totalCount: count,
    };

    getResponse(res, USER_ACTIONS.getInvoices, DOCUMENT_MESSAGES.getInvoices, data, meta);
  }

  public async searchInvoice(req: Request, res: Response) {
    const { orgId } = res.locals;

    const customerName = trimAndLowerCase(req.query.customerName as string);
    const invoiceNumber = extractIDocumentNumber(req.query.invoiceNumber as string);

    const { offset, page, limit } = pagination(req);

    const responseData = await this.invoiceServices.searchInvoice(
      orgId,
      customerName,
      invoiceNumber,
      offset,
      limit
    );
    if (!responseData) return;

    const { data, count } = responseData;
    const meta: ResponseMeta = {
      count: data.length,
      page,
      limit,
      totalCount: count,
    };

    return getResponse(res, USER_ACTIONS.searchInvoices, DOCUMENT_MESSAGES.getInvoices, data, meta);
  }
}
