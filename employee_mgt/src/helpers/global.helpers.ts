import { Request } from 'express';
import { APP_ROLES } from '../models/enums';

// export function getOrganizationIdFromRequestByRole(req: Request) {
//   let organizationId: string;

//   const role = String(req.user.role).toLocaleLowerCase();

//   if (role === APP_ROLES.SUPER_ADMIN) organizationId = req.params.organizationId as string;
//   else organizationId = req.user.organization.id;

//   return organizationId;
// }

export function getEmployeeIdFromRequest(req: Request) {
  //if super admin or owner

  if (req.user?.globalAccess || String(req.user?.role).toLowerCase() === APP_ROLES.OWNER) {
    return req.params.employeeId as string;
  }

  // if employee
  else return req.user.id as string;
}
