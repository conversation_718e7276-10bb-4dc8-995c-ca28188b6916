import { IEmployeeAttributes } from '../models/employee.model';
import { Op } from 'sequelize';
import { isValuePresent } from '../utilities/guards';

export function getEmployeeExistsErrorMessage(
  employee: IEmployeeAttributes,
  payload: Partial<IEmployeeAttributes>
) {
  const matchedFields: string[] = [];

  if (employee.email === payload.email && isValuePresent(payload.email))
    matchedFields.push('email');
  if (employee.phone_number === payload.phone_number && isValuePresent(payload.phone_number))
    matchedFields.push('phone_number');
  if (employee.national_id === payload.national_id && isValuePresent(payload.national_id))
    matchedFields.push('national_id');
  if (employee.tax_number === payload.tax_number && isValuePresent(payload.tax_number))
    matchedFields.push('tax_number');
  if (employee.tax_code === payload.tax_code && isValuePresent(payload.tax_code))
    matchedFields.push('tax_code');

  return `employee with ${matchedFields.join(', ')} exists`;
}

export function getEmployeeWhereParameters(orgId: string, payload: Partial<IEmployeeAttributes>) {
  const { email, phone_number, national_id, tax_number, tax_code } = payload;
  const organization_id = orgId;

  const conditions = [];

  if (isValuePresent(email)) {
    conditions.push({ email, organization_id });
  }

  if (isValuePresent(phone_number)) {
    conditions.push({ phone_number, organization_id });
  }

  if (isValuePresent(national_id)) {
    conditions.push({ national_id, organization_id });
  }

  if (isValuePresent(tax_number)) {
    conditions.push({ tax_number, organization_id });
  }

  if (isValuePresent(tax_code)) {
    conditions.push({ tax_code, organization_id });
  }

  // if (conditions.length === 0) {
  //   return {};
  // }

  return {
    [Op.or]: conditions,
  };
}
