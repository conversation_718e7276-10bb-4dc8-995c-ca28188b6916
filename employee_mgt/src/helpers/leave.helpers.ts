import { LEAVE_STATUS_ENUM } from '../models/enums';
import { startOfTheDay } from '../utilities/global.utilities';

export function determineApprovedLeaveStatus(leaveStartDate: string | Date) {
  const today = startOfTheDay(new Date());

  leaveStartDate = startOfTheDay(new Date(leaveStartDate));

  return today > leaveStartDate ? LEAVE_STATUS_ENUM.PENDING : LEAVE_STATUS_ENUM.IN_PROGRESS;
}

// export const validateLeaveStartDate = catchError((leaveStartDate: string | Date) => {
//   const today = new Date();
//   today.setHours(0, 0, 0, 0);

//   leaveStartDate = new Date(leaveStartDate);
//   leaveStartDate.setHours(0, 0, 0, 0);

//   return today >= leaveStartDate ? false : true;
// });
