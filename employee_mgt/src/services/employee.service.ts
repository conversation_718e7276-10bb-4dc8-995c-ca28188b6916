import sequelize from '../config/database/connection';
import Employee, { IEmployeeAttributes } from '../models/employee.model';
import Pension, { IPensionAttributes } from '../models/pension.model';
import Deductible, { IDeductibleAttributes } from '../models/deductible.model';
import { ERRORS } from '../constants/errors.constants';
import { ConflictError, InternalServerError, NotFoundError } from '../helpers/error.helpers';
import UtilityAPIs from '../api/endpoints/utilities.api';
import BaseServices from './base.service';
import { SERVICES } from '../constants/values.constants';
import Leave from '../models/leave.model';
import PaymentHistory from '../models/payment-history.model';
import { isValuePresent } from '../utilities/guards';
import { IEditEmployeeAttributes } from '../middlewares/validators/schemas/request_body/employee.schema';
import { ISearchEmployeeQuery } from '../middlewares/validators/schemas/query_params/employees.query.params.schema';
import EmployeePensionServices from './pension.service';
import EmployeeServiceUtils from './utils/employee.service.utils';
import EmployeeDeductibleServices from './deductibles.service';
import { EditEmployeeResult } from '../interfaces/global.interfaces';
import { Transaction } from 'sequelize';

export default class EmployeeServices extends BaseServices<Employee> {
  private attributesToExclude = ['organization_id'];

  constructor(
    private readonly pensionServices: EmployeePensionServices,
    private readonly deductibleServices: EmployeeDeductibleServices
  ) {
    super(Employee, SERVICES.employees);
  }

  async getAllEmployees(orgId: string, offset = 0, limit = 50) {
    return (await this.getManyAndCount({ organization_id: orgId }, { offset, limit }, true)) as {
      rows: IEmployeeAttributes[];
      count: number;
    };
  }

  async getOneEmployee(orgId: string, empId: string): Promise<IEmployeeAttributes> {
    return await this.getOne(
      {
        organization_id: orgId,
        id: empId,
      },
      {},
      true
    );
  }

  async getEmployeeWithRelatedData(orgId: string, empId: string): Promise<IEmployeeAttributes> {
    const fieldsToExclude = ['organization_id', 'employee_id', 'created_at', 'updated_at'];
    const attributes = { exclude: fieldsToExclude };
    const include = [
      {
        model: Pension,
        as: 'pension',
        attributes,
      },
      {
        model: Leave,
        as: 'leaves',
        attributes,
      },
      {
        model: Deductible,
        as: 'deductibles',
        attributes,
      },
      {
        model: PaymentHistory,
        as: 'payment_histories',
        attributes,
      },
    ];

    return await this.getOne(
      { organization_id: orgId, id: empId },
      { include, attributes: { exclude: this.attributesToExclude } },
      true
    );
  }

  async searchForOrgEmployees(
    orgId: string,
    offset = 0,
    limit = 50
  ): Promise<{ rows: IEmployeeAttributes[]; count: number }> {
    const options = {
      offset,
      limit,
      attributes: { exclude: this.attributesToExclude },
    };

    return await this.getManyAndCount({ organization_id: orgId }, options, true);
  }

  async searchForEmployees(
    orgId: string,
    searchParams: Partial<ISearchEmployeeQuery>,
    offset?: number,
    limit?: number
  ): Promise<{ rows: IEmployeeAttributes[]; count: number }> {
    // get the filter parameter
    const where = EmployeeServiceUtils.processSearchEmployeeWhereParameter(orgId, searchParams);

    return await this.getManyAndCount(
      where,
      {
        offset,
        limit,
        attributes: { exclude: ['organization_id'] },
      },
      true
    );
  }

  async changeEmploymentStatus(
    orgId: string,
    empId: string,
    newEmploymentStatus: string
  ): Promise<IEmployeeAttributes> {
    const employee = (await this.getOne(
      {
        organization_id: orgId,
        id: empId,
      },
      {},
      false
    )) as Employee;

    if (employee.employment_status === newEmploymentStatus) {
      throw new ConflictError(ERRORS.employeeStatusConflict);
    }

    employee.set({ employment_status: newEmploymentStatus });
    const updatedEmployee = (await this.saveInstance(employee, {}, true)) as IEmployeeAttributes;

    delete updatedEmployee.organization_id;

    return updatedEmployee;
  }

  async deleteOneEmployee(orgId: string, empId: string): Promise<boolean> {
    const employee = (await this.getOne(
      { organization_id: orgId, id: empId },
      {},
      false
    )) as Employee;

    await employee.destroy();
    return true;
  }

  async createOneEmployee(
    orgId: string,
    employeeDetails: Partial<IEmployeeAttributes>
  ): Promise<IEmployeeAttributes> {
    return await sequelize.transaction(async (transaction) => {
      // get where clause if unique values are present
      const whereClause = EmployeeServiceUtils.getSingleEmployeeDuplicateCheckingWhereParameters(
        orgId,
        employeeDetails
      );

      // only check if there is something to check
      if (whereClause) {
        const employeeExists = await this.getOneOrNull(whereClause, { transaction });

        if (employeeExists) {
          throw new ConflictError(
            EmployeeServiceUtils.getEmployeeExistsErrorForSingleEmployee(
              employeeExists,
              employeeDetails
            )
          );
        }
      }

      employeeDetails.organization_id = orgId;
      const { pension = {}, ...employee } = employeeDetails;

      const createdEmployee = (await this.create(
        employee,
        {
          transaction,
        },
        true
      )) as IEmployeeAttributes;

      if (!createdEmployee) {
        throw new InternalServerError('employee creation error');
      }

      const employeeData = { ...createdEmployee };

      if (isValuePresent(pension) && Object.keys(pension).length > 0) {
        const createdPension = await this.pensionServices.createOnePension(
          orgId,
          employeeData.id,
          pension,
          transaction
        );

        if (!createdPension) {
          throw new InternalServerError('pension creation error');
        }

        employeeData.pension = createdPension as IPensionAttributes;
      }

      return employeeData;
    });
  }

  async editOneEmployee(
    orgId: string,
    empId: string,
    payload: Partial<IEditEmployeeAttributes>
  ): Promise<EditEmployeeResult> {
    // validation happens here in service - ensure employee exists
    const existingEmployee = (await this.getOne(
      { organization_id: orgId, id: empId },
      {
        attributes: { exclude: this.attributesToExclude },
        include: [
          { model: Pension, as: 'pension' },
          { model: Deductible, as: 'deductibles' },
        ],
      },
      false
    )) as Employee;

    const existingDeductibles = (await this.deductibleServices.externalServiceGetDeductibles(
      orgId,
      empId,
      null,
      false
    )) as Deductible[];

    const { employee_details_update = {}, pension_update = {}, deductibles_update = [] } = payload;

    const result = await sequelize.transaction(async (transaction) => {
      let updatedEmployeeDetails: IEmployeeAttributes = null;
      let updatedPensionDetails: IPensionAttributes = null;

      const updatedDeductibleDetails: IDeductibleAttributes[] = [];

      if (Array.isArray(deductibles_update) && deductibles_update.length > 0) {
        const { deductiblesWithIds, deductiblesWithoutIds } =
          EmployeeServiceUtils.separateDeductibles(deductibles_update, orgId, empId);

        if (deductiblesWithIds.length > 0) {
          const existingDeductibleIds = existingDeductibles.map((deductible) => deductible.id);
          const idsOfProvidedDeductiblesWithId = deductiblesWithIds.map(
            (deductible) => deductible.id
          );

          const nonExistingProvidedIds = idsOfProvidedDeductiblesWithId.filter(
            (id) => !existingDeductibleIds.includes(id)
          );

          if (nonExistingProvidedIds.length > 0) {
            throw new NotFoundError(
              ERRORS.invalidDeductibleIdsForUpdate.replace(
                '<IDs>',
                nonExistingProvidedIds.join(', ')
              )
            );
          }

          for (const deductibleUpdate of deductiblesWithIds) {
            const matchingDeductible = existingDeductibles.find(
              (existingDeductible) => existingDeductible.id === deductibleUpdate.id
            );

            if (matchingDeductible) {
              delete deductibleUpdate.id;
              delete deductibleUpdate.organization_id;
              delete deductibleUpdate.employee_id;

              matchingDeductible.set(deductibleUpdate);
              updatedDeductibleDetails.push(
                (await matchingDeductible.save({ transaction })).get({ plain: true })
              );
            }
          }
        }

        updatedDeductibleDetails.push(
          ...(await this.deductibleServices.externalServiceBulkCreate(
            deductiblesWithoutIds,
            { transaction },
            true
          ))
        );
      }

      if (
        isValuePresent(employee_details_update) &&
        Object.keys(employee_details_update).length > 0
      ) {
        existingEmployee.set(employee_details_update);
        updatedEmployeeDetails = (await this.saveInstance(
          existingEmployee,
          { transaction },
          true
        )) as IEmployeeAttributes;
      }

      if (
        isValuePresent(existingEmployee.pension) &&
        isValuePresent(pension_update) &&
        Object.keys(pension_update).length > 0
      ) {
        const existingPension = (await this.pensionServices.externalServiceGetPension(
          orgId,
          empId,
          existingEmployee.pension.id,
          null,
          false
        )) as Pension;

        if (isValuePresent(existingPension)) {
          existingPension.set(pension_update);
          updatedPensionDetails = (await existingPension.save({ transaction })).get({
            plain: true,
          });
        }
      }

      return { updatedEmployeeDetails, updatedPensionDetails, updatedDeductibleDetails };
    });

    return result;
  }

  //create multiple employees
  async createMultipleEmployees(orgId: string, employees: Partial<IEmployeeAttributes>[]) {
    return await sequelize.transaction(async (transaction) => {
      // create employees only first
      const result = await this.bulkCreateEmployeesOnly(orgId, employees, transaction);

      if (!result.created && result.conflicts)
        return { created: false, conflicts: result.conflicts };

      // create pensions for employees that have pension data
      await this.bulkCreateEmployeePensions(orgId, employees, result.employees, transaction);
      return { created: true, employees: result.employees };
    });
  }

  private async bulkCreateEmployeesOnly(
    orgId: string,
    employeesPayload: Partial<IEmployeeAttributes>[],
    transaction: Transaction = null
  ) {
    // get existing employees checking where parameter
    const where = EmployeeServiceUtils.getMultipleEmployeeDuplicateCheckingWhereParameters(
      orgId,
      employeesPayload
    );

    // check if there are existing employees
    if (where) {
      const existingEmployees = await this.getMany(where, { transaction });

      if (existingEmployees.length > 0) {
        const conflicts = EmployeeServiceUtils.getEmployeeExistsErrorForMultipleEmployees(
          existingEmployees,
          employeesPayload
        );

        return { created: false, conflicts };
      }
    }

    // create employees in bulk
    const createdEmployees = (await this.bulkCreate(
      employeesPayload,
      { transaction, returning: true },
      true
    )) as IEmployeeAttributes[];

    if (!createdEmployees.length) {
      throw new InternalServerError('failed to create employees');
    }

    return { created: true, employees: createdEmployees };
  }

  private async bulkCreateEmployeePensions(
    orgId: string,
    employeesPayload: Partial<IEmployeeAttributes>[],
    createdEmployees: IEmployeeAttributes[],
    transaction: Transaction = null
  ): Promise<void> {
    // prepare pension data
    const pensionData = EmployeeServiceUtils.prepareBulkPensionData(
      employeesPayload,
      createdEmployees,
      orgId
    );

    // create pensions
    if (pensionData.length > 0) {
      const createdPensions = await Pension.bulkCreate(pensionData, {
        transaction,
        returning: true,
      });

      if (!createdPensions.length) {
        throw new InternalServerError('failed to create pension records');
      }
    }
  }

  //download bulk upload template
  async downloadMultipleEmployeeUploadTemplate(payload: {
    content: Record<string, any>[];
    workSheetName: string;
  }): Promise<Buffer> {
    return await UtilityAPIs.generateExcelFile(payload);
  }

  async exportEmployeeData(payload: {
    pages: { data: Record<string, any>[]; sheetName: string }[];
    filename: string;
  }): Promise<Buffer> {
    return await UtilityAPIs.generateMultiPageExcelFile(payload);
  }

  async sendInvitationToEmployee(orgId: string, empId: string, businessName: string) {
    const employee = (await this.getOne(
      {
        organization_id: orgId,
        id: empId,
      },
      {},
      false
    )) as Employee;

    const invitationLink = EmployeeServiceUtils.getEmployeeInvitationLink(employee.id);

    const form = EmployeeServiceUtils.getInvitationEmailForm(
      employee.get({ plain: true }),
      invitationLink,
      businessName
    );

    await UtilityAPIs.sendEmail(form);
    employee.set({ is_invited: true });

    return (await this.saveInstance(employee, {}, true)) as IEmployeeAttributes;
  }
}
