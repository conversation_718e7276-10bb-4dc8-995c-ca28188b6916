import { IEmployeeAttributes } from '../../models/employee.model';
import { ISearchEmployeeQuery } from '../../middlewares/validators/schemas/query_params/employees.query.params.schema';
import { isTestEnv, isValuePresent } from '../../utilities/guards';
import { Op, WhereOptions } from 'sequelize';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { IDeductibleAttributes } from '../../models/deductible.model';
import { IPensionAttributes } from '../../models/pension.model';
import { compileEmailHtml, generateEmailForm } from '../../utilities/global.utilities';
import {
  EMPLOYEE_CREATION_UNIQUE_FIELD_TO_DB_COLUMN_NAME_MAP,
  EMPLOYEES_CREATION_UNIQUE_FIELDS,
} from '../../constants/values.constants';
import { EMAIL_TEMPLATE_FILES, EMAIL_TEMPLATE_LAYOUT } from '../../constants/email.constants';

type EmployeeDuplicateValidationKeys = (typeof EMPLOYEES_CREATION_UNIQUE_FIELDS)[number];
type MultipleEmployeeDuplicateCheckingField = {
  [key in EmployeeDuplicateValidationKeys]: Set<string>;
};

export default class EmployeeServiceUtils extends ErrorWrapper {
  static processSearchEmployeeWhereParameter(
    orgId: string,
    searchParams: Partial<ISearchEmployeeQuery>
  ) {
    const where: WhereOptions = { organization_id: orgId };

    Object.entries(searchParams).forEach(([key, value]) => {
      if (isValuePresent(value)) {
        if (['employment_status', 'gender', 'employment_type', 'email'].includes(key)) {
          where[key] = { [Op.eq]: value as string };
        } else {
          where[key] = { [Op.iLike]: `%${value as string}%` };
        }
      }
    });

    return where;
  }

  static getSingleEmployeeDuplicateCheckingWhereParameters(
    orgId: string,
    payload: Partial<IEmployeeAttributes>
  ): WhereOptions<IEmployeeAttributes> | null {
    const { email, phone_number, national_id, tax_number, tax_code } = payload;

    const conditions: WhereOptions<IEmployeeAttributes> = [];

    if (isValuePresent(email)) {
      conditions.push({ email });
    }

    if (isValuePresent(phone_number)) {
      conditions.push({ phone_number });
    }

    if (isValuePresent(national_id)) {
      conditions.push({ national_id });
    }

    if (isValuePresent(tax_number)) {
      conditions.push({ tax_number });
    }

    if (isValuePresent(tax_code)) {
      conditions.push({ tax_code });
    }

    return conditions.length > 0 ? { organization_id: orgId, [Op.or]: conditions } : null;
  }

  // build the employee fields where duplicates will be checked.
  private static buildMultipleEmployeeDuplicateCheckingField(): MultipleEmployeeDuplicateCheckingField {
    const setOfFieldsToCheck = {} as MultipleEmployeeDuplicateCheckingField;

    EMPLOYEES_CREATION_UNIQUE_FIELDS.forEach(
      (field) => (setOfFieldsToCheck[field] = new Set<string>())
    );

    return setOfFieldsToCheck;
  }

  // build the employee fields where duplicate will be checked and update with values from payload.
  private static buildMultipleEmployeeDuplicateCheckingFieldsWithValues(
    employees: Partial<IEmployeeAttributes>[]
  ) {
    const fieldsToCheckWithValues = this.buildMultipleEmployeeDuplicateCheckingField();

    employees.forEach((employee) => {
      const { email, phone_number, national_id, tax_number, tax_code } = employee;
      if (isValuePresent(email)) fieldsToCheckWithValues.emails.add(email);
      if (isValuePresent(phone_number)) fieldsToCheckWithValues.phoneNumbers.add(phone_number);
      if (isValuePresent(national_id)) fieldsToCheckWithValues.nationalIds.add(national_id);
      if (isValuePresent(tax_number)) fieldsToCheckWithValues.taxNumbers.add(tax_number);
      if (isValuePresent(tax_code)) fieldsToCheckWithValues.taxCodes.add(tax_code);
    });

    return fieldsToCheckWithValues;
  }

  // build the where parameter that will be used on the db to get if duplicate exists for multiple employees
  static getMultipleEmployeeDuplicateCheckingWhereParameters(
    orgId: string,
    employees: Partial<IEmployeeAttributes>[]
  ): WhereOptions<IEmployeeAttributes> | null {
    const fieldsAndValues = this.buildMultipleEmployeeDuplicateCheckingFieldsWithValues(employees);

    const conditions: WhereOptions<IEmployeeAttributes>[] = [];

    for (const [field, values] of Object.entries(fieldsAndValues)) {
      if (values.size > 0) {
        conditions.push({
          [EMPLOYEE_CREATION_UNIQUE_FIELD_TO_DB_COLUMN_NAME_MAP[
            field as EmployeeDuplicateValidationKeys
          ]]: { [Op.in]: [...values] },
        });
      }
    }

    // if (fieldsToCheckWithValues.emails.size > 0)
    //   conditions.push({ email: { [Op.in]: [...fieldsToCheckWithValues.emails] } });

    // if (fieldsToCheckWithValues.phoneNumbers.size > 0)
    //   conditions.push({ phone_number: { [Op.in]: [...fieldsToCheckWithValues.phoneNumbers] } });

    // if (fieldsToCheckWithValues.nationalIds.size > 0)
    //   conditions.push({ national_id: { [Op.in]: [...fieldsToCheckWithValues.nationalIds] } });

    // if (fieldsToCheckWithValues.taxNumbers.size > 0)
    //   conditions.push({ tax_number: { [Op.in]: [...fieldsToCheckWithValues.taxNumbers] } });

    // if (fieldsToCheckWithValues.taxCodes.size > 0)
    //   conditions.push({ tax_code: { [Op.in]: [...fieldsToCheckWithValues.taxCodes] } });

    return conditions.length > 0 ? { organization_id: orgId, [Op.or]: conditions } : null;
  }

  static getEmployeeExistsErrorForSingleEmployee(
    foundEmployee: IEmployeeAttributes,
    payload: Partial<IEmployeeAttributes>
  ): string {
    const matchedFields: string[] = [];

    if (foundEmployee.email === payload.email && isValuePresent(payload.email))
      matchedFields.push('email');
    if (foundEmployee.phone_number === payload.phone_number && isValuePresent(payload.phone_number))
      matchedFields.push('phone_number');
    if (foundEmployee.national_id === payload.national_id && isValuePresent(payload.national_id))
      matchedFields.push('national_id');
    if (foundEmployee.tax_number === payload.tax_number && isValuePresent(payload.tax_number))
      matchedFields.push('tax_number');
    if (foundEmployee.tax_code === payload.tax_code && isValuePresent(payload.tax_code))
      matchedFields.push('tax_code');

    return `employee with ${matchedFields.join(', ')} exists`;
  }

  static getEmployeeExistsErrorForMultipleEmployees(
    existingEmployees: IEmployeeAttributes[],
    employeesPayload: Partial<IEmployeeAttributes>[]
  ) {
    const conflictFieldsMap = this.buildMultipleEmployeeDuplicateCheckingField();
    const payloadFieldsMap = this.buildMultipleEmployeeDuplicateCheckingField();

    employeesPayload.forEach((employee) => {
      const { email, phone_number, national_id, tax_number, tax_code } = employee;

      // add values to the payload field set
      if (isValuePresent(email)) payloadFieldsMap.emails.add(email);
      if (isValuePresent(phone_number)) payloadFieldsMap.phoneNumbers.add(phone_number);
      if (isValuePresent(national_id)) payloadFieldsMap.nationalIds.add(national_id);
      if (isValuePresent(tax_number)) payloadFieldsMap.taxNumbers.add(tax_number);
      if (isValuePresent(tax_code)) payloadFieldsMap.taxCodes.add(tax_code);
    });

    existingEmployees.forEach((existingEmployee) => {
      const { email, phone_number, national_id, tax_number, tax_code } = existingEmployee;

      // check and add conflicting values to the conflict fields map
      if (isValuePresent(email))
        payloadFieldsMap.emails.has(email) && conflictFieldsMap.emails.add(email);

      if (isValuePresent(phone_number))
        payloadFieldsMap.phoneNumbers.has(phone_number) &&
          conflictFieldsMap.phoneNumbers.add(phone_number);

      if (isValuePresent(national_id))
        payloadFieldsMap.nationalIds.has(national_id) &&
          conflictFieldsMap.nationalIds.add(national_id);

      if (isValuePresent(tax_number))
        payloadFieldsMap.taxNumbers.has(tax_number) && conflictFieldsMap.taxNumbers.add(tax_number);

      if (isValuePresent(tax_code))
        payloadFieldsMap.taxCodes.has(tax_code) && conflictFieldsMap.taxCodes.add(tax_code);
    });

    return {
      emails: [...conflictFieldsMap.emails],
      phone_numbers: [...conflictFieldsMap.phoneNumbers],
      national_ids: [...conflictFieldsMap.nationalIds],
      tax_codes: [...conflictFieldsMap.taxCodes],
      tax_numbers: [...conflictFieldsMap.taxNumbers],
    };
  }

  // separate deductibles into those with and without IDs for bulk operations
  static separateDeductibles(
    deductibles: Partial<IDeductibleAttributes>[],
    orgId: string,
    empId: string
  ) {
    const deductiblesWithoutIds: Partial<IDeductibleAttributes>[] = [];
    const deductiblesWithIds: Partial<IDeductibleAttributes>[] = [];

    deductibles.forEach((deductible) => {
      deductible.organization_id = orgId;
      deductible.employee_id = empId;

      if (deductible.id) {
        deductiblesWithIds.push(deductible);
      } else {
        deductiblesWithoutIds.push(deductible);
      }
    });

    return { deductiblesWithoutIds, deductiblesWithIds };
  }

  // prepare bulk pension data for creation
  static prepareBulkPensionData(
    employeesPayload: Partial<IEmployeeAttributes>[],
    createdEmployees: IEmployeeAttributes[],
    orgId: string
  ): Partial<IPensionAttributes>[] {
    const pensionData: Partial<IPensionAttributes>[] = [];

    employeesPayload.forEach((eachEmployeePayload, index) => {
      if (isValuePresent(eachEmployeePayload.pension) && createdEmployees[index]) {
        pensionData.push({
          ...eachEmployeePayload.pension,
          organization_id: orgId,
          employee_id: createdEmployees[index].id,
        });
      }
    });

    return pensionData;
  }

  // send invitation email to employee
  static getInvitationEmailForm(
    employee: IEmployeeAttributes,
    invitationLink: string,
    businessName: string
  ) {
    const html = compileEmailHtml(
      EMAIL_TEMPLATE_FILES.employeeInvitation,
      { ...employee, businessName, invitationLink },
      EMAIL_TEMPLATE_LAYOUT.no_logo
    );

    const emailOptions = {
      to: employee?.email,
      subject: `invitation to join ${businessName} on Digit-Tally`,
      html,
    };

    return generateEmailForm(emailOptions);
  }

  static getEmployeeInvitationLink(employeeId: string) {
    if (isTestEnv) {
      return `https://stgsvr.digit-tally.io/employees/invited/${employeeId}`;
    }

    return `https://app.digit-tally.io/employees/invited/${employeeId}`;
  }
}
