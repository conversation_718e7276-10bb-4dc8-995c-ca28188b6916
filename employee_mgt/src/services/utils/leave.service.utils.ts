import { ERRORS } from '../../constants/errors.constants';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { ConflictError } from '../../helpers/error.helpers';
import { LEAVE_APPROVAL_STATUS_ENUM, LEAVE_STATUS_ENUM } from '../../models/enums';
import { ILeaveAttributes } from '../../models/leave.model';
import {
  endOfTheDay,
  getTimeDifferenceWithUnit,
  startOfTheDay,
} from '../../utilities/global.utilities';

export default class EmployeeLeaveServiceUtils extends ErrorWrapper {
  static validateLeaveCancelling(existingStatus: string): void {
    if (existingStatus === LEAVE_STATUS_ENUM.CANCELLED) {
      throw new ConflictError(ERRORS.leaveIsCancelled);
    }

    if (existingStatus === LEAVE_STATUS_ENUM.AWAITING_APPROVAL) {
      throw new ConflictError(ERRORS.leaveIsAwaitingApproval);
    }

    if (existingStatus === LEAVE_STATUS_ENUM.COMPLETED) {
      throw new ConflictError(ERRORS.leaveIsCompleted);
    }

    if (existingStatus === LEAVE_STATUS_ENUM.REJECTED) {
      throw new ConflictError(ERRORS.leaveIsRejected);
    }

    return;
  }

  static addCreateLeaveDetails(
    leave: Partial<ILeaveAttributes>,
    orgId: string,
    empId: string
  ): Partial<ILeaveAttributes> {
    const leaveCopy: Partial<ILeaveAttributes> = { ...leave };

    leaveCopy.organization_id = orgId;
    leaveCopy.employee_id = empId;

    leaveCopy.start_date = startOfTheDay(new Date(leaveCopy.start_date));
    leaveCopy.end_date = endOfTheDay(new Date(leaveCopy.end_date));

    leaveCopy.status = LEAVE_STATUS_ENUM.AWAITING_APPROVAL;

    const timeDifferenceAndUnit = getTimeDifferenceWithUnit(
      leaveCopy.start_date,
      leaveCopy.end_date
    );

    leaveCopy.length = `${timeDifferenceAndUnit.value} ${timeDifferenceAndUnit.unit}`;

    return leaveCopy;
  }

  static addEditLeaveDetails(leaveUpdates: Partial<ILeaveAttributes>) {
    const updateCopy: Partial<ILeaveAttributes> = { ...leaveUpdates };

    updateCopy.start_date = startOfTheDay(new Date(updateCopy.start_date));
    updateCopy.end_date = endOfTheDay(new Date(updateCopy.end_date));

    const timeDifferenceAndUnit = getTimeDifferenceWithUnit(
      updateCopy.start_date,
      updateCopy.end_date
    );

    updateCopy.length = `${timeDifferenceAndUnit.value} ${timeDifferenceAndUnit.unit}`;

    return updateCopy;
  }

  private static determineApprovedLeaveStatus(leaveStartDate: string | Date) {
    const today = startOfTheDay(new Date());

    leaveStartDate = startOfTheDay(new Date(leaveStartDate));

    return today > leaveStartDate ? LEAVE_STATUS_ENUM.PENDING : LEAVE_STATUS_ENUM.IN_PROGRESS;
  }

  static determineLeaveApprovalStatus(
    approvalStatus: LEAVE_APPROVAL_STATUS_ENUM,
    leaveStartDate: string | Date
  ) {
    return approvalStatus === LEAVE_APPROVAL_STATUS_ENUM.APPROVED
      ? this.determineApprovedLeaveStatus(new Date(leaveStartDate))
      : LEAVE_STATUS_ENUM.REJECTED;
  }
}
