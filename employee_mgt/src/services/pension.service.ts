import BaseServices from './base.service';
import { SERVICES } from '../constants/values.constants';
import Pension, { IPensionAttributes } from '../models/pension.model';
import { Transaction } from 'sequelize';

export default class EmployeePensionServices extends BaseServices<Pension> {
  private attributesToExclude = ['organization_id', 'employee_id'];

  constructor() {
    super(Pension, SERVICES.pensions);
  }

  private removeUnwantedPensionDetails(pension: Partial<IPensionAttributes>) {
    const copy = { ...pension };

    delete copy.organization_id;
    delete copy.employee_id;

    return copy;
  }

  // async getPension();

  async getEmployeePension(orgId: string, empId: string): Promise<Partial<IPensionAttributes>> {
    const pension = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
      },
      { attributes: { exclude: this.attributesToExclude } }
    )) as IPensionAttributes;

    return pension;
  }

  async getOnePension(
    orgId: string,
    empId: string,
    pensionId: string
  ): Promise<Partial<IPensionAttributes>> {
    const pension = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: pensionId,
      },
      { attributes: { exclude: this.attributesToExclude } }
    )) as IPensionAttributes;

    return this.removeUnwantedPensionDetails(pension);
  }

  async createOnePension(
    orgId: string,
    empId: string,
    pensionDetails: Partial<IPensionAttributes>,
    transaction: Transaction = null
  ) {
    pensionDetails.organization_id = orgId;
    pensionDetails.employee_id = empId;

    const createdPension = (await this.create(
      pensionDetails,
      { transaction },
      true
    )) as IPensionAttributes;

    return this.removeUnwantedPensionDetails(createdPension);
  }

  async editOnePension(
    orgId: string,
    empId: string,
    pensionId: string,
    updates: Partial<IPensionAttributes>,
    transaction: Transaction = null
  ): Promise<Partial<IPensionAttributes>> {
    const pension = (await this.getOne(
      {
        organization_id: orgId,
        employee_id: empId,
        id: pensionId,
      },
      { transaction },
      false
    )) as Pension;

    pension.set(updates);
    const updatedPension = (await this.saveInstance(pension)) as IPensionAttributes;

    return this.removeUnwantedPensionDetails(updatedPension);
  }

  async deleteOnePension(orgId: string, empId: string, pensionId: string) {
    const pension = (await this.getOne(
      { organization_id: orgId, employee_id: empId, id: pensionId },
      {},
      false
    )) as Pension;

    await pension.destroy();

    return true;
  }

  async externalServiceGetPension(
    orgId: string,
    empId: string,
    pensionId: string,
    transaction: Transaction = null,
    plain: boolean = true
  ): Promise<IPensionAttributes | Pension> {
    return await this.getOneOrNull(
      { organization_id: orgId, employee_id: empId, id: pensionId },
      { transaction },
      plain
    );
  }

  async externalServiceGetPensions(
    orgId: string,
    empId: string,
    transaction: Transaction = null,
    plain: boolean = true
  ): Promise<(IPensionAttributes | Pension)[]> {
    return await this.getMany(
      { organization_id: orgId, employee_id: empId },
      { transaction },
      plain
    );
  }
}
