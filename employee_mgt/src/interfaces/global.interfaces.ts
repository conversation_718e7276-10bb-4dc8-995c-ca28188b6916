// import { catchAsync } from '../utilities/catch-async-error';

import { IDeductibleAttributes } from '../models/deductible.model';
import { IEmployeeAttributes } from '../models/employee.model';
import { IPensionAttributes } from '../models/pension.model';

export interface EmailFormOptions {
  to: string | string[];
  from?: string;
  subject: string;
  text?: string;
  html?: string;
  attachments?: { buffer: Buffer; filename: string }[];
}

export interface EmailResponse {
  status: string;
  message: string;
}

export interface IMicroServiceInternalAuthPayload {
  appName: string;
  [key: string]: any;
}

export interface Meta {
  count?: number;
  limit?: number;
  page?: number;
  totalCounts?: number;
  [key: string]: any;
}

export interface EditEmployeeResult {
  updatedEmployeeDetails: IEmployeeAttributes | null;
  updatedPensionDetails: IPensionAttributes | null;
  updatedDeductibleDetails: IDeductibleAttributes[];
}
