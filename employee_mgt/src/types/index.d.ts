import { COUNTRY_NAMES_AND_TWO_LETTER_CODES } from '../constants/values.constants';
import { NOTIFICATION_COLLECTIONS } from '../constants/notification.constants';

export type Country2LetterCode =
  (typeof COUNTRY_NAMES_AND_TWO_LETTER_CODES)[keyof typeof COUNTRY_NAMES_AND_TWO_LETTER_CODES];

type NotificationScope = 'USER' | 'ORGANISATION';

type TaskPriority = 'LOW' | 'MEDIUM' | 'HIGH';

// type for notification collections derived from the constants
export type NotificationCollection =
  (typeof NOTIFICATION_COLLECTIONS)[keyof typeof NOTIFICATION_COLLECTIONS];

export interface NotificationAttributes {
  user_ids?: string[];
  org_id: string;
  title: string;
  user_id: string;
  message: string;
  emit_event?: string;
  type: NotificationScope;
  priority: TaskPriority;
  event_name: string;
  collection: NotificationCollection;
  exclude_users?: Array<string>;
}
