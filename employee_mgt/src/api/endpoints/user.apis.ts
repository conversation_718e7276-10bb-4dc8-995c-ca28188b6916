import { HTTP_METHODS } from '../../constants/values.constants';
import { getRequestOptions } from '../helpers';
import axiosInstance from '../../config/axios';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { USER_API_URLS } from '../urls';
import { IUser } from '../../interfaces/user.interfaces';
// import { isTestENV } from '../../utilities/guards';

export default class UserAPI extends ErrorWrapper {
  //get account details
  public static async getMyAccount(): Promise<IUser> {
    const method = HTTP_METHODS.GET;
    const url = USER_API_URLS.getMyAccount;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      accessKey,
      api<PERSON>ey,
    });
    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  public static async getUserByParams(params: { role: string } | { organizationId: number }) {
    const method = HTTP_METHODS.GET;
    const url = USER_API_URLS.getUserByParams;
    const accessKey = process.env.USERS_SERVICE_KEY;
    const apiKey = process.env.GATEWAY_API_KEY;
    // const authHeader = httpContext.get('authHeader');

    const options = getRequestOptions({ method, url, params, reqId: true, accessKey, apiKey });

    const response = await axiosInstance.request(options);

    return response.data.data;
  }
}
