import * as Sentry from '@sentry/node';
import logger from '../../utilities/logger';
import { Request, Response } from 'express';
import { sendErrorResponse } from '../../helpers/response.helpers';
import { NextFunction } from 'express';
import {
  BaseError,
  UniqueConstraintError,
  ValidationError,
  ForeignKeyConstraintError,
} from 'sequelize';
import {
  AppError,
  BadRequestError,
  ConflictError,
  InternalServerError,
} from '../../helpers/error.helpers';

function handleInternalServerError(): AppError {
  return new InternalServerError();
}

function handleSequelizeError(err: BaseError): AppError {
  if (err instanceof UniqueConstraintError) {
    const [key, value] = Object.entries(err.fields)[0];

    return new ConflictError(
      `employee with ${key} = ${value} exists`,
      'handling sequelize error: unique constraint error'
    );
  }

  if (err instanceof ValidationError) {
    return new BadRequestError(
      `${err.errors.map((e) => e.message).join(', ')}`,
      'handling sequelize error: validation error'
    );
  }

  if (err instanceof ForeignKeyConstraintError) {
    return new InternalServerError(
      undefined,
      'handling sequelize error: foreign key constraint error'
    );
  }

  return handleInternalServerError();
}

function handleError(err: Error): AppError {
  if (err instanceof AppError) {
    return err;
  }

  if (err instanceof BaseError) {
    return handleSequelizeError(err);
  }

  return handleInternalServerError();
}

function logError(req: Request, err: Error): void {
  let errDetails: Record<string, any> = {
    url: req.originalUrl,
    method: req.method,
    body: req.body,
    ip: req.ip,
    errorName: err.name,
    message: err.message,
  };

  if (err instanceof AppError) {
    errDetails = {
      ...errDetails,
      statusCode: err?.statusCode,
      errorLocation: err?.location,
    };
  } else if (err instanceof ValidationError || err instanceof UniqueConstraintError) {
    errDetails = {
      ...errDetails,
      name: 'Sequelize Validation OR Unique Constraint Error',
      errors: err?.errors.map((e) => e.message),
      fields: (err as UniqueConstraintError).fields || {},
    };
  } else if (err instanceof ForeignKeyConstraintError) {
    errDetails = {
      ...errDetails,
      name: 'Sequelize Foreign Key Constraint Error',
      fields: err.fields || {},
    };
  }

  errDetails['error'] = { name: err?.name, stack: err?.stack };

  logger.error(errDetails);
}

async function globalErrorHandler(
  error: Error,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction
) {
  Sentry.captureException(error);

  // Log error and convert to AppError
  logError(req, error);
  const appError = handleError(error);

  return sendErrorResponse(res, appError);
}

export default globalErrorHandler;
