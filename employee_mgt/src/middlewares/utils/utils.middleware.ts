import os from 'os';
import { NextFunction, Request, Response } from 'express';
import geoip from 'geoip-lite';
import {
  getPublicAddress,
  getUserAgentHeader,
  getUserTimeZone,
} from '../../utilities/global.utilities';
import { format, toZonedTime } from 'date-fns-tz';
import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { IUser, OrganizationMember } from '../../interfaces/user.interfaces';
import { NotAuthenticatedError, NotFoundError } from '../../helpers/error.helpers';
import services from '../../containers/services.container';
import { ERRORS } from '../../constants/errors.constants';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      userTimeZone: string;
      user: IUser;
    }
  }
}

export interface DeviceDetails {
  ip: string;
  userAgent: string;
  browser: string;
  os: string;
  timezone: string;
  time: string;
  location: string;
}

export default class UtilityMiddlewares extends RequestHandlerErrorWrapper {
  private employeeService = services.resolve('employeeService');

  private captureDeviceDetails(req: Request, res: Response) {
    const ip = getPublicAddress(req) || 'Unknown';
    const userAgent = getUserAgentHeader(req);
    const browser =
      RegExp(/(Firefox|Chrome|Safari|Opera|MSIE|Trident)/i).exec(userAgent)?.[0] || 'Unknown';
    const os = RegExp(/\(([^)]+)\)/).exec(userAgent)?.[1] || 'Unknown';

    const geo = geoip.lookup(ip);
    const location: string = geo?.country || 'US';
    const timezone = geo?.timezone || 'UTC';
    const time = format(toZonedTime(new Date(), timezone), 'yyyy-MM-dd HH:mm:ssXXX');

    const deviceDetails: DeviceDetails = {
      ip,
      userAgent,
      browser,
      os,
      timezone,
      time,
      location,
    };
    return (res.locals = { ...res.locals, deviceDetails });
  }

  private getRequestLogDetails(req: Request, res: Response) {
    const { deviceDetails } = res.locals;
    const { ip, userAgent, browser, os: userOS, time, timezone } = deviceDetails;
    const { method, originalUrl: url, body, hostname } = req;

    const createdAt = time;

    const serverIp = req.ip;
    const serverName = os.hostname();
    const serverPlatform = os.platform();
    const serverMemory = os.totalmem();
    const serverCpuCount = os.cpus().length;

    // const user = req.user
    //   ? Object.keys(req.user).length > 0
    //     ? { id: req.user.uuid, organizationId: req.user.organization.id, email: req.user.email }
    //     : { anonymous: true }
    //   : { anonymous: true };

    const user = { anonymous: true };

    const userDetails = { ...user };

    const requestDetails = {
      ipAddress: ip,
      userAgent,
      browser,
      os: userOS,
      hostname,
      timezone,
      method,
      url,
      body,
      createdAt,
    };

    const serverDetails = {
      ipAddress: serverIp,
      name: serverName,
      platform: serverPlatform,
      memory: serverMemory,
      cpuCount: serverCpuCount,
      server_time: new Date(),
    };

    return (res.locals = {
      ...res.locals,
      requestLogDetails: { userDetails, requestDetails, serverDetails },
    });
  }

  async captureAppDetails(req: Request, res: Response, next: NextFunction) {
    req.userTimeZone = getUserTimeZone(req);
    this.captureDeviceDetails(req, res);
    this.getRequestLogDetails(req, res);
    next();
  }

  async extractOrgIdAndMembersFromUser(req: Request, res: Response, next: NextFunction) {
    const user = req?.user;

    if (!user) throw new NotAuthenticatedError();

    const orgId: string = req.user.organization.id;
    const orgMembers: OrganizationMember[] = req.user.organizationMembers;

    res.locals = {
      ...res.locals,
      orgId,
      orgMembers,
      organizationId: orgId,
    };

    next();
  }

  async extractEmployeeFromRequest(req: Request, res: Response, next: NextFunction) {
    const orgId = res.locals.orgId as string;
    const employeeId = req.params.employeeId as string;

    const employee = await this.employeeService.getOneEmployee(orgId, employeeId);

    if (!employee) {
      throw new NotFoundError(ERRORS.employeeNotFound);
    }

    res.locals = { ...res.locals, employee };

    next();
  }
}
