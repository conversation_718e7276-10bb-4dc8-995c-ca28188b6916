import UserAP<PERSON> from '../../api/endpoints/user.apis';
import { ERRORS } from '../../constants/errors.constants';
import { exemptFromErrorWrapping, RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { NotAuthenticatedError, NotPermittedError } from '../../helpers/error.helpers';
import { Request, Response, NextFunction, RequestHandler } from 'express';
import httpContext from 'express-http-context';
import { catchAsync } from '../../utilities/catch-async-error';
import { IUser, SUBSCRIPTION_STATUS } from '../../interfaces/user.interfaces';
import { HTTP_METHODS } from '../../constants/values.constants';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user: IUser;
      orgSubIsActive: boolean;
    }
  }
}

export default class AuthMiddlewares extends RequestHandlerErrorWrapper {
  async authenticateUser(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];

    if (!authHeader) throw new NotAuthenticatedError();

    httpContext.set('authHeader', authHeader);
    const authenticatedUser = await UserAPI.getMyAccount();

    if (!authenticatedUser) return;

    req.user = { ...authenticatedUser };
    const { organization, organizationMembers, ...userInfo } = authenticatedUser;

    if (!organization) throw new NotPermittedError(ERRORS.noOrganizationError);

    const logUserDetails = {
      userId: req.user.id,
      orgId: req.user.organization.id,
      email: req.user.email,
    };

    res.locals = {
      ...res.locals,
      organization,
      organizationMembers,
      userInfo,
      requestLogDetails: { ...res.locals.requestLogDetails, userDetails: logUserDetails },
    };

    const { subscription } = organization;

    req.orgSubIsActive = subscription
      ? subscription?.access &&
        !subscription?.viewOnly &&
        subscription?.status === SUBSCRIPTION_STATUS.ACTIVE &&
        new Date(subscription.expiresAt) > new Date()
      : false;

    next();
  }

  @exemptFromErrorWrapping
  verifyPermission(allowedRoles: string[]): RequestHandler {
    return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
      allowedRoles = allowedRoles.map((allowedRole) => allowedRole.toLowerCase());

      const userRole = String(req.user.role).toLowerCase();

      if (!allowedRoles.includes(userRole)) {
        throw new NotPermittedError();
      }

      next();
    });
  }

  async restrictToAdminAccess(req: Request, res: Response, next: NextFunction) {
    if (!req?.user.globalAccess) throw new NotPermittedError();

    next();
  }

  // async verifyAdminAccess(req: Request, res: Response, next: NextFunction) {
  //   const token = req.headers['x-dgt-internal-auth-token'];

  //   if (!token || typeof token !== 'string') {
  //     throw new NotAuthenticatedError();
  //   }

  //   const payload = verifyInternalAuthToken(token);

  //   if (payload.appName !== 'adminApp') {
  //     throw new NotPermittedError();
  //   }

  //   next();
  // }

  async validateActiveSubscription(req: Request, _res: Response, next: NextFunction) {
    if (req.method !== HTTP_METHODS.GET) {
      if (!req.user) {
        throw new NotAuthenticatedError();
      }

      if (!req.orgSubIsActive) {
        throw new NotPermittedError(ERRORS.requiresActiveSubscriptionError);
      }
    }
    next();
  }
}
