import { COUNTRY_NAMES_AND_TWO_LETTER_CODES } from '../../../constants/values.constants';
import Joi, { ObjectSchema } from 'joi';
import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';

type changeOptions = {
  exclude?: string[];
  keepRequired?: string[];
};

export function makeSchemaFieldsOptional(
  schema: ObjectSchema,
  options: changeOptions = {}
): ObjectSchema {
  const { exclude = [], keepRequired = [] } = options;

  const schemaDescription = schema.describe();

  const schemaKeysList = Object.keys(schemaDescription.keys || {});

  const transformedFields = schemaKeysList.reduce(
    (acc, key) => {
      if (exclude.includes(key)) return acc;

      let fieldSchema = schema.extract(key) as Joi.AnySchema;

      // recursively transform nested object
      if (fieldSchema.type === 'object') {
        fieldSchema = makeSchemaFieldsOptional(fieldSchema as ObjectSchema, options);
      }

      // recursively transform array item schema
      else if (fieldSchema.type === 'array') {
        const itemDesc = (fieldSchema as Joi.ArraySchema).describe().items?.[0];
        if (itemDesc) {
          const itemSchema = Joi.build(itemDesc) as Joi.AnySchema;

          const transformedItemSchema = makeSchemaFieldsOptional(
            itemSchema as ObjectSchema,
            options
          );

          fieldSchema = (fieldSchema as Joi.ArraySchema).items(transformedItemSchema);
        }
      }

      // make optional and strip default
      fieldSchema = fieldSchema.optional().prefs({ noDefaults: true });

      // re-apply required if specified
      if (keepRequired.includes(key)) {
        fieldSchema = fieldSchema.required();
      }

      acc[key] = fieldSchema;
      return acc;
    },
    {} as Record<string, Joi.AnySchema>
  );

  return Joi.object(transformedFields);
}

export const trimmedString = Joi.string().trim().lowercase();

export function getCountry2LetterCode(countryName: string): CountryCode | null {
  countryName = countryName.trim().toLowerCase();
  return COUNTRY_NAMES_AND_TWO_LETTER_CODES[`${countryName}`] || null;
}

export function parseValidateFormatPhoneNumber(
  phoneNumber: string,
  countryCode?: CountryCode
): string | null {
  const parsed = parsePhoneNumberFromString(phoneNumber);

  if (countryCode && parsed.country !== countryCode) return null;

  return parsed?.isValid() ? parsed.format('E.164') : null;
}
