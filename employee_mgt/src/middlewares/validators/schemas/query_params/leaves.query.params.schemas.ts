import { LEAVE_APPROVAL_STATUS_ARRAY } from '../../../../models/enums';
import <PERSON><PERSON> from 'joi';

export const approveOrRejectLeaveQuerySchema = Joi.object<{ approvalStatus: string }>({
  approvalStatus: Joi.string()
    .valid(...LEAVE_APPROVAL_STATUS_ARRAY)
    .required()
    .messages({
      'string.base': 'approval status must be a string',
      'string.empty': 'approval status cannot be empty',
      'any.required': 'approval status is required as a query parameter',
      'any.only': `approval status can only be ${LEAVE_APPROVAL_STATUS_ARRAY.join(' or ')}`,
    }),
});
