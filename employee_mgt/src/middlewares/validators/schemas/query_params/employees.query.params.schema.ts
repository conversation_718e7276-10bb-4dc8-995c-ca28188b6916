import Joi, { CustomHelpers } from 'joi';
import {
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_TYPE_ARRAY,
  GENDER_ARRAY,
} from '../../../../models/enums';
import { trimmedString } from '../helpers.schema';

export const employeeStatusQuerySchema = Joi.object<{ employment_status: string }>({
  employment_status: Joi.string()
    .valid(...EMPLOYMENT_STATUS_ARRAY)
    .required()
    .messages({
      'any.required': 'employment_status is required as a query parameter.',
      'string.base': 'employment_status must be a string.',
      'any.only': `employment_status must be either : ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}.`,
    }),
});

export interface ISearchEmployeeQuery {
  email?: string;
  first_name?: string;
  last_name?: string;
  employment_status?: string;
  role?: string;
  gender?: string;
  employment_type?: string;
}

export const searchEmployeeQuerySchema = Joi.object<ISearchEmployeeQuery>({
  email: trimmedString.email({ minDomainSegments: 2 }).optional().messages({
    'string.base': 'email must be a string',
    'string.email': 'email must be a valid email address.',
  }),
  first_name: trimmedString.min(2).max(30).optional().messages({
    'string.base': 'first name must be a string.',
    'string.min': 'first name must be at least {#limit} characters long.',
    'string.max': 'first name must not exceed {#limit} characters.',
  }),
  last_name: trimmedString.min(2).max(30).optional().messages({
    'string.base': 'last name must be a string.',
    'string.min': 'last name must be at least {#limit} characters long.',
    'string.max': 'last name must not exceed {#limit} characters.',
  }),
  gender: Joi.string()
    .lowercase()
    .valid(...GENDER_ARRAY)
    .optional()
    .messages({
      'string.base': 'gender must be a string.',
      'any.only': `gender must be one of: ${GENDER_ARRAY.join(', ')}.`,
    }),
  employment_status: Joi.string()
    .lowercase()
    .valid(...EMPLOYMENT_STATUS_ARRAY)
    .optional()
    .messages({
      'string.base': 'employment_status must be a string.',
      'any.only': `employment_status must be either : ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}.`,
    }),
  role: trimmedString.min(2).max(30).optional().messages({
    'string.base': 'role must be a string.',
    'string.min': 'role must be at least {#limit} characters long.',
    'string.max': 'role must not exceed {#limit} characters.',
  }),
  employment_type: Joi.string()
    .valid(...EMPLOYMENT_TYPE_ARRAY)
    .optional()
    .messages({
      'string.base': 'employment type must be a string.',
      'any.only': `employment type must be one of: ${EMPLOYMENT_TYPE_ARRAY.join(', ')}.`,
    }),
})
  .custom((value: ISearchEmployeeQuery, helpers: CustomHelpers) => {
    if (Object.keys(value).length === 0) {
      return helpers.error('object.empty');
    }
    return value;
  })
  .messages({
    'object.empty': 'at least one search parameter is required',
    'object.base': 'search parameters must be an object',
  });
