import Joi from 'joi';
import { createDeductibleCustomValidator } from '../../custom-validators.helpers';
import { IDeductibleAttributes } from '../../../../models/deductible.model';

export const createDeductibleSchema = Joi.object<IDeductibleAttributes>({
  reason: Joi.string().trim().lowercase().min(2).max(100).required().messages({
    'any.required': 'reason is required.',
    'string.base': 'reason must be a string.',
    'string.min': 'reason must be at least {#limit} characters long.',
    'string.max': 'reason must not exceed {#limit} characters.',
  }),

  value: Joi.number().precision(2).positive().required().messages({
    'any.required': 'value is required.',
    'number.base': 'value must be a valid number.',
    'number.positive': 'value must be greater than zero.',
    'number.precision': 'value must have at most 2 decimal places.',
  }),

  start_date: Joi.date().iso().required().messages({
    'any.required': 'start date is required',
    'date.base': 'start date must be a valid iso date.',
  }),

  end_date: Joi.date().iso().required().messages({
    'any.required': 'end date is required.',
    'date.base': 'end date must be a valid iso date.',
  }),

  one_time: Joi.boolean().default(false).optional().messages({
    'boolean.base': 'one-time must be a boolean value.',
  }),
})
  .custom(createDeductibleCustomValidator, 'create deductible custom validator')
  .messages({
    'date.oneTime': 'if one_time is true, start_date and end_date must be the same.',
    'date.notOneTime':
      'if one_time is false, end date must be equal to or greater than start date.',
  });

//update deductible schema
export const editDeductibleSchema = Joi.object<IDeductibleAttributes>({});
