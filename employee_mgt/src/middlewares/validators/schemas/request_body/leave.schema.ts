import Joi, { CustomHelpers } from 'joi';
import { ILeaveAttributes } from '../../../../models/leave.model';
import { LEAVE_TYPE_ARRAY } from '../../../../models/enums';
import {
  createLeaveCustomValidator,
  requireAtLeastOneField,
} from '../../custom-validators.helpers';
import { endOfTheDay, startOfTheDay } from '../../../../utilities/global.utilities';
import { makeSchemaFieldsOptional } from '../helpers.schema';

// create leave schema
export const createLeaveSchema = Joi.object<ILeaveAttributes>({
  type: Joi.string()
    .valid(...LEAVE_TYPE_ARRAY)
    .required()
    .trim()
    .lowercase()
    .messages({
      'string.base': 'leave type must be a string.',
      'any.only': `leave type must be one of: ${LEAVE_TYPE_ARRAY.join(', ')}.`,
      'any.required': 'leave type is required.',
    }),

  start_date: Joi.date()
    .iso()
    .required()
    .custom((value: string, helper: CustomHelpers) => {
      const startDate = startOfTheDay(new Date(value));
      const endOfToday = endOfTheDay(new Date());

      if (endOfToday >= startDate) {
        return helper.error('date.not.future');
      }

      return value;
    })
    .messages({
      'date.base': 'start date must be a valid date.',
      'date.format': 'start date must be in iso format (yyyy-mm-dd).',
      'any.required': 'start date is required.',
      'date.not.future': 'start date is not a future date.',
    }),

  end_date: Joi.date().iso().greater(Joi.ref('start_date')).required().messages({
    'date.base': 'end date must be a valid date.',
    'date.format': 'end date must be in iso format (yyyy-mm-dd).',
    'date.greater': 'end date must be after the start date.',
    'any.required': 'end date is required.',
  }),
})
  .custom(createLeaveCustomValidator, 'create leave custom validator')
  .messages({
    'end.date.lesser.than.start.date': 'end date must be after the start date.',
  });

// update leave schema
export const editLeaveSchema = makeSchemaFieldsOptional(createLeaveSchema)
  .custom(requireAtLeastOneField)
  .messages({
    'any.base': 'edit leave details must be an object',
    'any.atLeastOneField': 'at least one leave field is required.',
  });
