import { NextFunction, Request, Response } from 'express';
import { catchAsync } from '../../utilities/catch-async-error';
import { Schema, ValidationError } from 'joi';
import { BadRequestError } from '../../helpers/error.helpers';
import { validate as isValidUUID } from 'uuid';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      validatedQueryParams: Record<string, any>;
    }
  }
}

export function getJoiValidationErrorMessage(error: ValidationError) {
  const errorMessage = error.details
    .map((detail) => {
      return detail.message.replace(/"+/g, '');
    })
    .join(', ');

  return errorMessage;
}

export function validateRequestBody(schema: Schema) {
  return catchAsync(async (req: Request, res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.body, { stripUnknown: true });

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.body = value;
    next();
  });
}

export function validateQueryParams(schema: Schema) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const { value, error } = schema.validate(req.query, { stripUnknown: true, abortEarly: true });

    if (error) {
      throw new BadRequestError(getJoiValidationErrorMessage(error));
    }

    req.validatedQueryParams = value;
    next();
  });
}

export function validateRouteIdParam(name: string) {
  return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
    const id = req.params[`${name}`];

    if (!id || typeof id !== 'string' || !isValidUUID(id)) {
      throw new BadRequestError(
        `${name} is required as a route parameter and must be a valid UUID string`
      );
    }

    next();
  });
}
