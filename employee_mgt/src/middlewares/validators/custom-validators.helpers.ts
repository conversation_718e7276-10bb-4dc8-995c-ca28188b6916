import { CustomHelpers } from 'joi';
import {
  EMPLOYMENT_TYPE_ENUM,
  KIND_OF_PAYMENT_ENUM,
  MODE_OF_PAYMENT_ENUM,
} from '../../models/enums';
import { ILeaveAttributes } from '../../models/leave.model';
import { IEmployeeAttributes } from '../../models/employee.model';
import { IDeductibleAttributes } from '../../models/deductible.model';
import { IPensionAttributes } from '../../models/pension.model';
import { IEditEmployeeAttributes } from './schemas/request_body/employee.schema';
import { isValuePresent } from '../../utilities/guards';
import {
  COUNTRY_NAMES_AND_TWO_LETTER_CODES,
  EMPLOYEES_CREATION_UNIQUE_FIELDS,
} from '../../constants/values.constants';
import { parseValidateFormatPhoneNumber } from './schemas/helpers.schema';

export const createEmployeeCustomValidator = (
  value: IEmployeeAttributes,
  helper: CustomHelpers
) => {
  //  if employment type is contract, employment_end_date must be present
  if (value.employment_type === EMPLOYMENT_TYPE_ENUM.CONTRACT && !value.employment_end_date) {
    return helper.error('no.employment.end.date');
  }

  //  if mode of payment is electronic, bank details must be present
  if (value.mode_of_payment === MODE_OF_PAYMENT_ENUM.ELECTRONIC) {
    if (!value.bank_name || !value.bank_account_name || !value.bank_account_number) {
      return helper.error('no.bank.details');
    }
  }

  // validate phone number with country
  if (isValuePresent(value.country) && isValuePresent(value.phone_number)) {
    const country = value.country;
    const phoneNumber = value.phone_number;

    const countryCode = COUNTRY_NAMES_AND_TWO_LETTER_CODES[`${country}`] ?? null;
    if (!countryCode) return helper.error('country.not.known');

    const validPhoneNumber = parseValidateFormatPhoneNumber(phoneNumber, countryCode);
    if (!validPhoneNumber) return helper.error('phone.number.not.valid');
  }

  //  if any bank details are present, all three must be present
  const bankDetails = [value.bank_name, value.bank_account_name, value.bank_account_number];
  const bankDetailsPresent = bankDetails.filter((detail) => detail && detail.trim() !== '').length;
  if (bankDetailsPresent > 0 && bankDetailsPresent < 3) {
    return helper.error('incomplete.bank.details');
  }

  // if kind of payment is hourly, hourly_rate and work_hours_per_week must be present
  if (value.kind_of_payment === KIND_OF_PAYMENT_ENUM.HOURLY) {
    if (!value.hourly_rate || !value.work_hours_per_week) {
      return helper.error('no.rate.or.work.hours');
    }
  }

  if (value.salary && value.hourly_rate) {
    return helper.error('one.kind.of.payment');
  }

  if (value.kind_of_payment === KIND_OF_PAYMENT_ENUM.SALARY && !value.salary) {
    return helper.error('salary.required');
  }

  return value;
};

export const createLeaveCustomValidator = (value: ILeaveAttributes, helper: CustomHelpers) => {
  //end date must be greater than or equal to start date
  if (
    value.end_date &&
    value.start_date &&
    new Date(value.end_date).setHours(0, 0, 0, 0) < new Date(value.start_date).setHours(0, 0, 0, 0)
  ) {
    return helper.error('end.date.lesser.than.start.date');
  }
  return value;
};

export const createDeductibleCustomValidator = (
  value: IDeductibleAttributes,
  helper: CustomHelpers
) => {
  let { one_time, start_date, end_date } = value;

  one_time = one_time || false;
  start_date = new Date(start_date);
  end_date = new Date(end_date);

  if (one_time && start_date !== end_date) {
    return helper.error('date.oneTime');
  }

  if (!one_time && end_date <= start_date) {
    return helper.error('date.notOneTime');
  }

  return value;
};

export const editEmployeeCustomValidator = (
  value: IEditEmployeeAttributes,
  helper: CustomHelpers
) => {
  if (!value.employee_details_update && !value.pension_update && !value.deductibles_update) {
    return helper.error('any.required');
  }
  return value;
};

export const editPensionCustomValidator = (
  value: Partial<IPensionAttributes>,
  helper: CustomHelpers
) => {
  if (Object.keys(value).length === 0) {
    return helper.error('any.atLeastOneField');
  }
  return value;
};

export const requireAtLeastOneField = (value: Record<string, any>, helper: CustomHelpers) => {
  if (Object.keys(value).length === 0) {
    return helper.error('any.atLeastOneField');
  }

  return value;
};

export function phoneNumberCustomValidator(value: string, helper: CustomHelpers) {
  const isValidNumber = parseValidateFormatPhoneNumber(value);

  if (!isValidNumber) {
    return helper.error('phone.number.invalid');
  }

  return isValidNumber;
}

type FieldKeys = (typeof EMPLOYEES_CREATION_UNIQUE_FIELDS)[number];
type FieldMap = { [key in FieldKeys]: Map<string, number> };
type ErrorCodeMap = { [key in FieldKeys]: `duplicate.${FieldKeys}` };

function buildFieldsToVerifyAndErrorCodes() {
  const fieldMap = {} as FieldMap;
  const errorCodeMap = {} as ErrorCodeMap;

  EMPLOYEES_CREATION_UNIQUE_FIELDS.forEach((fieldName) => {
    fieldMap[fieldName] = new Map<string, number>();
    errorCodeMap[fieldName] = `duplicate.${fieldName}`;
  });

  return { fieldMap, errorCodeMap };
}

export function createMultipleEmployeesCustomValidator(
  value: Partial<IEmployeeAttributes>[],
  helper: CustomHelpers
) {
  const { fieldMap: fieldsToValidate, errorCodeMap } = buildFieldsToVerifyAndErrorCodes();

  for (const e of value) {
    if (e.email)
      fieldsToValidate.emails.set(e.email, (fieldsToValidate.emails.get(e.email) || 0) + 1);
    if (e.phone_number)
      fieldsToValidate.phoneNumbers.set(
        e.phone_number,
        (fieldsToValidate.phoneNumbers.get(e.phone_number) || 0) + 1
      );
    if (e.national_id)
      fieldsToValidate.nationalIds.set(
        e.national_id,
        (fieldsToValidate.nationalIds.get(e.national_id) || 0) + 1
      );
    if (e.tax_number)
      fieldsToValidate.taxNumbers.set(
        e.tax_number,
        (fieldsToValidate.taxNumbers.get(e.tax_number) || 0) + 1
      );
    if (e.tax_code)
      fieldsToValidate.taxCodes.set(
        e.tax_code,
        (fieldsToValidate.taxCodes.get(e.tax_code) || 0) + 1
      );
  }

  for (const [field, map] of Object.entries(fieldsToValidate)) {
    for (const [value, count] of map.entries()) {
      if (count > 1) {
        return helper.error(errorCodeMap[field], { value, count });
      }
    }
  }

  return value;
}

// export function createMultipleEmployeesCustomValidator(
//   value: Partial<IEmployeeAttributes>[],
//   helper: CustomHelpers
// ) {
//   const fieldsToValidate = {
//     email: new Map<string, number>(),
//     phone_number: new Map<string, number>(),
//     national_id: new Map<string, number>(),
//     tax_number: new Map<string, number>(),
//     tax_code: new Map<string, number>(),
//   };

//   for (const e of value) {
//     if (e.email)
//       fieldsToValidate.email.set(e.email, (fieldsToValidate.email.get(e.email) || 0) + 1);
//     if (e.phone_number)
//       fieldsToValidate.phone_number.set(
//         e.phone_number,
//         (fieldsToValidate.phone_number.get(e.phone_number) || 0) + 1
//       );
//     if (e.national_id)
//       fieldsToValidate.national_id.set(
//         e.national_id,
//         (fieldsToValidate.national_id.get(e.national_id) || 0) + 1
//       );
//     if (e.tax_number)
//       fieldsToValidate.tax_number.set(
//         e.tax_number,
//         (fieldsToValidate.tax_number.get(e.tax_number) || 0) + 1
//       );
//     if (e.tax_code)
//       fieldsToValidate.tax_code.set(
//         e.tax_code,
//         (fieldsToValidate.tax_code.get(e.tax_code) || 0) + 1
//       );
//   }

//   for (const [field, map] of Object.entries(fieldsToValidate)) {
//     for (const [, count] of map.entries()) {
//       if (count > 1) {
//         return helper.error(`duplicate.${field}`);
//       }
//     }
//   }

//   return value;
// }
