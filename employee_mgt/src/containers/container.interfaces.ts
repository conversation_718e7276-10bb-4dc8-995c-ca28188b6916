import EmployeeDeductibleControllers from '../controllers/deductible.controller';
import EmployeeControllers from '../controllers/employee.controller';
import EmployeeLeaveControllers from '../controllers/leave.controller';
import EmployeePaymentHistoryControllers from '../controllers/payment-history.controller';
import EmployeePensionControllers from '../controllers/pension.controller';
import UtilityControllers from '../controllers/utilities.controller';
import AuthMiddlewares from '../middlewares/auth/auth.middleware';
import CacheMiddlewares from '../middlewares/utils/cache.middleware';
import UtilityMiddlewares from '../middlewares/utils/utils.middleware';
import EmployeeDeductibleServices from '../services/deductibles.service';
import EmployeeServices from '../services/employee.service';
import EmployeeLeaveServices from '../services/leave.service';
import EmployeePaymentHistoryServices from '../services/payment-history.service';
import EmployeePensionServices from '../services/pension.service';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';

//add all services instances typing
export interface ServiceInstances {
  employeeService: EmployeeServices;
  employeeLeaveService: EmployeeLeaveServices;
  employeePaymentHistoryService: EmployeePaymentHistoryServices;
  employeePensionService: EmployeePensionServices;
  employeeDeductibleService: EmployeeDeductibleServices;
  backgroundTaskManagers: BackgroundTaskManager;
}

//add all controllers instances typing
export interface ControllerInstances {
  utilityController: UtilityControllers;
  employeeController: EmployeeControllers;
  employeeLeaveController: EmployeeLeaveControllers;
  employeePaymentHistoryController: EmployeePaymentHistoryControllers;
  employeePensionController: EmployeePensionControllers;
  employeeDeductibleController: EmployeeDeductibleControllers;
}

//add all middleware instances typing
export interface MiddlewareInstances {
  authMiddleware: AuthMiddlewares;
  cacheMiddleware: CacheMiddlewares;
  utilityMiddleware: UtilityMiddlewares;
}

export interface ContainerInstances {
  controllers: ControllerInstances;
  services: ServiceInstances;
  middlewares: MiddlewareInstances;
}
