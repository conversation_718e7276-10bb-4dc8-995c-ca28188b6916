import Container from './container.global';
import services from './services.container';
import UtilityControllers from '../controllers/utilities.controller';
import EmployeeControllers from '../controllers/employee.controller';
import EmployeeLeaveControllers from '../controllers/leave.controller';
import EmployeePaymentHistoryControllers from '../controllers/payment-history.controller';
import EmployeePensionControllers from '../controllers/pension.controller';
import EmployeeDeductibleControllers from '../controllers/deductible.controller';

const controllers = new Container('controllers');
const employeeService = services.resolve('employeeService');
const leaveService = services.resolve('employeeLeaveService');
const paymentHistoryService = services.resolve('employeePaymentHistoryService');
const pensionService = services.resolve('employeePensionService');
const deductibleService = services.resolve('employeeDeductibleService');

//register all controllers here
controllers.register('utilityController', new UtilityControllers());

controllers.register('employeeController', new EmployeeControllers(employeeService));

controllers.register('employeeLeaveController', new EmployeeLeaveControllers(leaveService));

controllers.register(
  'employeePaymentHistoryController',
  new EmployeePaymentHistoryControllers(paymentHistoryService)
);

controllers.register('employeePensionController', new EmployeePensionControllers(pensionService));

controllers.register(
  'employeeDeductibleController',
  new EmployeeDeductibleControllers(deductibleService)
);

export default controllers;
