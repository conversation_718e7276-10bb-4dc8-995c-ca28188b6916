import EmployeeDeductibleServices from '../services/deductibles.service';
import EmployeeServices from '../services/employee.service';
import EmployeeLeaveServices from '../services/leave.service';
import EmployeePaymentHistoryServices from '../services/payment-history.service';
import EmployeePensionServices from '../services/pension.service';
import BackgroundTaskManager from '../utilities/background-tasks/background-tasks-manager.utility';
import Container from './container.global';

//register all services here
const services = new Container('services');

services.register('employeePensionService', new EmployeePensionServices());

services.register('employeeLeaveService', new EmployeeLeaveServices());

services.register('employeePaymentHistoryService', new EmployeePaymentHistoryServices());

services.register('employeeDeductibleService', new EmployeeDeductibleServices());

services.register(
  'employeeService',
  new EmployeeServices(
    services.resolve('employeePensionService'),
    services.resolve('employeeDeductibleService')
  )
);

services.register('backgroundTaskManagers', new BackgroundTaskManager());

export default services;
