import { Request, Response } from 'express';
import {
  capitalizeFirstLetter,
  getPagination,
  sendOrganizationNotification,
} from '../utilities/global.utilities';
import EmployeeLeaveServices from '../services/leave.service';
import { sendJSONResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { ILeaveAttributes } from '../models/leave.model';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { USER_ACTIONS } from '../constants/values.constants';
import { Meta } from '../interfaces/global.interfaces';
import { IEmployeeAttributes } from '../models/employee.model';
import { LEAVES } from '../constants/notification.constants';

export default class EmployeeLeaveControllers extends RequestHandlerErrorWrapper {
  constructor(private leaveServices: EmployeeLeaveServices) {
    super();
  }

  async getAllLeaves(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;

    const { page, offset, limit } = getPagination(req);
    // const userTimezone = req.userTimeZone;

    const data = await this.leaveServices.getAllLeaves(orgId, employee.id, offset, limit);

    const meta: Meta = { page, limit, count: data.rows.length, totalCounts: data.count };

    // const response = { msgAndCode: RESPONSES.leavesRetrieved, leaves, meta };

    // const employeeIsPresent = employeeId ? employeeId : false;
    // await this.leaveService.cacheGetAll(
    //   organizationId,
    //   response,
    //   offset,
    //   limit,
    //   userTimezone,
    //   employeeIsPresent
    // );

    return sendJSONResponse(res, RESPONSES.leavesRetrieved, USER_ACTIONS.getLeave, data.rows, meta);
  }

  async createOneLeave(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const payload = req.body as ILeaveAttributes;

    const createdLeave = await this.leaveServices.createOneLeave(orgId, employee.id, payload);

    await sendOrganizationNotification(
      res,
      'Leave created',
      `${req.user.email} has created a leave record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'MEDIUM',
      LEAVES
    );

    // await this.leaveService.clearCachedData(organizationId);
    return sendJSONResponse(res, RESPONSES.leaveCreated, USER_ACTIONS.createLeave, createdLeave);
  }

  async editOneLeave(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;

    const leaveId = req.params.leaveId;
    const payload = req.body as Partial<ILeaveAttributes>;

    const editedLeave = await this.leaveServices.editOneLeave(orgId, employee.id, leaveId, payload);

    await sendOrganizationNotification(
      res,
      'Leave updated',
      `${req.user.email} has updated a leave record for employee: ${employee?.first_name} ${employee?.last_name}`,
      'MEDIUM',
      LEAVES
    );

    // await this.leaveService.clearCachedData(organizationId);
    return sendJSONResponse(res, RESPONSES.leaveUpdated, USER_ACTIONS.editLeave, editedLeave);
  }

  async getOneLeave(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;

    const leaveId = req.params.leaveId;

    const leave = await this.leaveServices.getOneLeave(orgId, employee.id, leaveId);

    return sendJSONResponse(res, RESPONSES.leavesRetrieved, USER_ACTIONS.getLeave, leave);
  }

  async cancelLeave(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;

    const leaveId = req.params.leaveId;

    const leave = await this.leaveServices.cancelLeave(orgId, employee.id, leaveId);

    await sendOrganizationNotification(
      res,
      'Leave Cancelled',
      `${req.user.email} has cancelled a leave for employee:${employee.first_name} ${employee.last_name}`,
      'HIGH',
      LEAVES
    );

    return sendJSONResponse(res, RESPONSES.leaveStatusUpdated, USER_ACTIONS.editLeave, leave);
  }

  async approveOrRejectLeave(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const leaveId = req.params.leaveId;
    const approvalStatus = req.query.approvalStatus as string;

    const leave = await this.leaveServices.approveOrRejectLeave(
      orgId,
      employee.id,
      leaveId,
      approvalStatus
    );

    await sendOrganizationNotification(
      res,
      `Leave ${capitalizeFirstLetter(approvalStatus)}ed`,
      `${req.user.email} has ${approvalStatus}ed leave for employee: ${employee?.first_name} ${employee?.last_name}`,
      'MEDIUM',
      LEAVES
    );

    // await this.leaveService.clearCachedData(organizationId);
    return sendJSONResponse(res, RESPONSES.leaveStatusUpdated, USER_ACTIONS.editLeave, leave);
  }
}
