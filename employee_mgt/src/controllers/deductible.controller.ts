import { Request, Response } from 'express';
import EmployeeDeductibleServices from '../services/deductibles.service';
import { RESPONSES } from '../constants/responses.constants';
import { sendJSONResponse } from '../helpers/response.helpers';
import { getPagination, sendOrganizationNotification } from '../utilities/global.utilities';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { USER_ACTIONS } from '../constants/values.constants';
import { Meta } from '../interfaces/global.interfaces';
import { IDeductibleAttributes } from '../models/deductible.model';
import { IEmployeeAttributes } from '../models/employee.model';
import { DEDUCTIBLES } from '../constants/notification.constants';

export default class EmployeeDeductibleControllers extends RequestHandlerErrorWrapper {
  constructor(private deductibleService: EmployeeDeductibleServices) {
    super();
  }

  async getAllDeductibles(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const { page, offset, limit } = getPagination(req);
    // const userTimeZone = req.userTimeZone;

    // get all deductibles for the specified employee
    const result = await this.deductibleService.getAllDeductibles(
      orgId,
      employee.id,
      offset,
      limit
    );

    const meta: Meta = {
      count: result.rows.length,
      page,
      limit,
      totalCounts: result.count,
    };

    return sendJSONResponse(
      res,
      RESPONSES.deductiblesRetrieved,
      USER_ACTIONS.getDeductible,
      result.rows,
      meta
    );
  }

  async getOneDeductible(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const deductibleId = req.params.deductibleId;

    // get specific deductible for the employee
    const deductible = await this.deductibleService.getOneDeductible(
      orgId,
      employee.id,
      deductibleId
    );

    return sendJSONResponse(
      res,
      RESPONSES.deductiblesRetrieved,
      USER_ACTIONS.getDeductible,
      deductible
    );
  }

  async createMultipleDeductibles(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const payload = req.body as Partial<IDeductibleAttributes>[];

    // create multiple deductibles for the employee
    // note: deductible uniqueness validation should be implemented in the service layer
    const deductibles = await this.deductibleService.createOneDeductible(
      orgId,
      employee.id,
      payload
    );

    // send notification about deductible creation
    await sendOrganizationNotification(
      res,
      'deductibles created',
      `${req.user.email} has created ${deductibles.length} deductible(s) for employee`,
      'MEDIUM',
      DEDUCTIBLES
    );

    return sendJSONResponse(
      res,
      RESPONSES.deductiblesCreated,
      USER_ACTIONS.createDeductible,
      deductibles
    );
  }

  async editOneDeductible(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const deductibleId = req.params.deductibleId;
    const payload = req.body as Partial<IDeductibleAttributes>;

    // update specific deductible for the employee
    const deductible = await this.deductibleService.updateOneDeductible(
      orgId,
      employee.id,
      deductibleId,
      payload
    );

    // send notification about deductible update
    await sendOrganizationNotification(
      res,
      'deductible updated',
      `${req.user.email} has updated a deductible for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      DEDUCTIBLES
    );

    return sendJSONResponse(
      res,
      RESPONSES.deductiblesUpdated,
      USER_ACTIONS.editDeductible,
      deductible
    );
  }

  async deleteOneDeductible(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const deductibleId = req.params.deductibleId;

    // delete the deductible
    await this.deductibleService.deleteOneDeductible(orgId, employee.id, deductibleId);

    // send notification about deductible deletion
    await sendOrganizationNotification(
      res,
      'deductible deleted',
      `${req.user.email} has deleted a deductible for employee: ${employee?.first_name} ${employee?.last_name}`,
      'HIGH',
      DEDUCTIBLES
    );

    return sendJSONResponse(res, RESPONSES.deductiblesDeleted, USER_ACTIONS.deleteDeductible);
  }
}
