import { Request, Response } from 'express';
import EmployeeService from '../services/employee.service';
import { getPagination, sendOrganizationNotification } from '../utilities/global.utilities';
import { IEmployeeAttributes } from '../models/employee.model';
import { sendErrorWithData, sendFileResponse, sendJSONResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { CREATE_EMPLOYEE_TEMPLATE, USER_ACTIONS } from '../constants/values.constants';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { getEmployeeIdFromRequest } from '../helpers/global.helpers';
import { ISearchEmployeeQuery } from '../middlewares/validators/schemas/query_params/employees.query.params.schema';
import { Meta } from '../interfaces/global.interfaces';
import { IEditEmployeeAttributes } from '../middlewares/validators/schemas/request_body/employee.schema';
import { StatusCodes } from 'http-status-codes';

export default class EmployeeControllers extends RequestHandlerErrorWrapper {
  constructor(private employeeService: EmployeeService) {
    super();
  }

  async createOneEmployee(req: Request, res: Response) {
    const { orgId } = res.locals;
    const employeeDetails = req.body as Partial<IEmployeeAttributes>;

    const createdEmployee = await this.employeeService.createOneEmployee(orgId, employeeDetails);

    await sendOrganizationNotification(
      res,
      'employee created',
      `${req.user.email} has created employee: ${createdEmployee.first_name} ${createdEmployee.last_name}`
    );

    // await this.employeeService.clearCachedData(orgId);
    return sendJSONResponse(
      res,
      RESPONSES.employeesCreated,
      USER_ACTIONS.createEmployee,
      createdEmployee
    );
  }

  async createMultipleEmployees(req: Request, res: Response) {
    const { orgId } = res.locals;
    const payload = req.body as { employees: Partial<IEmployeeAttributes>[] };

    const result = await this.employeeService.createMultipleEmployees(orgId, payload.employees);

    if (!result.created && result.conflicts)
      return sendErrorWithData(
        res,
        StatusCodes.CONFLICT,
        'employee(s) with some details already exists',
        result.conflicts
      );

    const createdEmployees = result.employees;

    await sendOrganizationNotification(
      res,
      'Multiple Employees Created',
      `${req.user.email} has created ${createdEmployees.length} employees.`
    );

    // await this.employeeService.clearCachedData(orgId);
    return sendJSONResponse(
      res,
      RESPONSES.employeesCreated,
      USER_ACTIONS.createBulkEmployees,
      createdEmployees
    );
  }

  async getAllEmployees(req: Request, res: Response) {
    const { orgId } = res.locals;
    const { page, offset, limit } = getPagination(req);

    const result = await this.employeeService.getAllEmployees(orgId, offset, limit);

    const meta: Meta = {
      count: result.rows.length,
      page,
      limit,
      totalCounts: result.count,
    };

    // await this.employeeService.cacheGetAll(
    //   organizationId,
    //   response,
    //   offset,
    //   limit,
    //   req.userTimezone
    // );

    return sendJSONResponse(
      res,
      RESPONSES.employeesRetrieved,
      USER_ACTIONS.getEmployee,
      result.rows,
      meta
    );
  }

  async getOneEmployee(req: Request, res: Response) {
    const { orgId } = res.locals;
    const empId = getEmployeeIdFromRequest(req);

    const employee = await this.employeeService.getEmployeeWithRelatedData(orgId, empId);

    return sendJSONResponse(res, RESPONSES.employeesRetrieved, USER_ACTIONS.getEmployee, employee);
  }

  async editOneEmployee(req: Request, res: Response) {
    const { orgId } = res.locals;
    const empId = getEmployeeIdFromRequest(req);

    const payload = req.body as Partial<IEditEmployeeAttributes>;

    const updateResult = await this.employeeService.editOneEmployee(orgId, empId, payload);

    await sendOrganizationNotification(
      res,
      'Employee updated',
      `${req.user.email} has updated the details of an employee with id: ${updateResult.updatedEmployeeDetails.id}`,
      'HIGH'
    );

    // await this.employeeService.clearCachedData(orgId);
    return sendJSONResponse(
      res,
      RESPONSES.employeeUpdated,
      USER_ACTIONS.editEmployee,
      updateResult
    );
  }

  async changeEmployeeEmploymentStatus(req: Request, res: Response) {
    const { orgId } = res.locals;
    const empId = getEmployeeIdFromRequest(req);
    const newEmploymentStatus = String(req.query.employment_status).toLowerCase();

    const updatedEmployee = await this.employeeService.changeEmploymentStatus(
      orgId,
      empId,
      newEmploymentStatus
    );

    await sendOrganizationNotification(
      res,
      'Employee status changed',
      `${req.user.email} has changed employment status to ${newEmploymentStatus} for: ${updatedEmployee.first_name} ${updatedEmployee.last_name}`,
      'HIGH'
    );

    // await this.employeeService.clearCachedData(organizationId);
    return sendJSONResponse(
      res,
      RESPONSES.employeeStatusChanged,
      USER_ACTIONS.editEmployeeStatus,
      updatedEmployee
    );
  }

  async searchForEmployees(req: Request, res: Response) {
    const { orgId } = res.locals;
    const { page, offset, limit } = getPagination(req);

    const searchParam = req.validatedQueryParams as Partial<ISearchEmployeeQuery>;

    const employees = await this.employeeService.searchForEmployees(
      orgId,
      searchParam,
      offset,
      limit
    );
    const meta: Meta = { page, limit, count: employees.rows.length, totalCounts: employees.count };

    return sendJSONResponse(
      res,
      RESPONSES.employeesRetrieved,
      USER_ACTIONS.searchEmployee,
      employees.rows,
      meta
    );
  }

  async downloadMultipleEmployeeUploadTemplate(_req: Request, res: Response) {
    const buffer = await this.employeeService.downloadMultipleEmployeeUploadTemplate({
      content: [...CREATE_EMPLOYEE_TEMPLATE],
      workSheetName: 'create bulk employee template',
    });

    return sendFileResponse(res, USER_ACTIONS.downloadMultipleEmployeeUploadTemplate, {
      filename: 'create-bulk-employee-template.xlsx',
      buffer,
    });
  }

  // async exportEmployeeData(req: Request, res: Response) {
  //   const organizationId = res.locals.organizationId as string;
  //   const userTimezone = req.userTimeZone;

  //   //TODO: verify if business exists with organization id.

  //   const pages: { data: Record<string, any>[]; sheetName: string }[] = [];

  //   const allEmployees = await this.employeeService.getAllEmployees(organizationId);

  //   pages.push({
  //     data: convertCreateAtAndUpdatedAtToUserTimezone(allEmployees, userTimezone),
  //     sheetName: 'employee-details-format',
  //   });

  //   const allPensions = await this.pensionService.getAllOrgPensions(organizationId);

  //   pages.push({
  //     data: convertCreateAtAndUpdatedAtToUserTimezone(allPensions, userTimezone),
  //     sheetName: 'pension-details',
  //   });

  //   const deductibles = await this.deductibleService.getAllOrgDeductibles(organizationId);

  //   pages.push({
  //     data: convertCreateAtAndUpdatedAtToUserTimezone(deductibles, userTimezone),
  //     sheetName: 'deductible-details',
  //   });

  //   const filename = `${organizationId}-employees-data.xlsx`;
  //   const buffer = await this.employeeService.exportEmployeeData({ pages, filename });
  //   return sendFileResponse(res, USER_ACTIONS.exportEmployeeData, { filename, buffer });
  // }

  async inviteEmployee(req: Request, res: Response) {
    const { orgId } = res.locals;
    const empId = getEmployeeIdFromRequest(req);
    const businessName = req.user.organization.name;

    // get employee details for notification
    const employee = await this.employeeService.getOneEmployee(orgId, empId);

    await this.employeeService.sendInvitationToEmployee(orgId, empId, businessName);

    await sendOrganizationNotification(
      res,
      'Employee invitation sent',
      `${req.user.email} sent an invitation to employee: ${employee.first_name} ${employee.last_name}`,
      'LOW'
    );

    return sendJSONResponse(res, RESPONSES.invitationSent, USER_ACTIONS.inviteEmployee);
  }
}
