import { RESPONSES } from '../constants/responses.constants';
import { sendJSONResponse } from '../helpers/response.helpers';

import EmployeePaymentHistoryServices from '../services/payment-history.service';
import { Request, Response } from 'express';
import { getPagination } from '../utilities/global.utilities';
import { IPaymentHistoryAttributes } from '../models/payment-history.model';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import { USER_ACTIONS } from '../constants/values.constants';
import { Meta } from '../interfaces/global.interfaces';
import { IEmployeeAttributes } from '../models/employee.model';

export default class EmployeePaymentHistoryControllers extends RequestHandlerErrorWrapper {
  constructor(private paymentHistoryService: EmployeePaymentHistoryServices) {
    super();
  }

  async getAllPaymentHistories(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    // const userTimezone = req.userTimeZone;

    const { page, offset, limit } = getPagination(req);

    const result = await this.paymentHistoryService.getAllPaymentHistories(
      orgId,
      employee.id,
      offset,
      limit
    );
    const meta: Meta = { page, count: result.rows.length, limit, totalCounts: result.count };
    // const response = {
    //   msgAndCode: RESPONSES.paymentHistoriesRetrieved,
    //   paymentHistories,
    //   meta,
    // };

    // const employeeIsPresent = employeeId ? employeeId : false;
    // await this.paymentHistoryService.cacheGetAll(
    //   organizationId,
    //   response,
    //   offset,
    //   limit,
    //   userTimezone,
    //   employeeIsPresent
    // );

    return sendJSONResponse(
      res,
      RESPONSES.paymentHistoriesRetrieved,
      USER_ACTIONS.getPaymentHistory,
      result.rows,
      meta
    );
  }

  async getOnePaymentHistory(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const paymentHistoryId = req.params.paymentHistoryId;

    const paymentHistory = await this.paymentHistoryService.getOnePaymentHistory(
      orgId,
      employee.id,
      paymentHistoryId
    );

    return sendJSONResponse(
      res,
      RESPONSES.paymentHistoriesRetrieved,
      USER_ACTIONS.getPaymentHistory,
      paymentHistory
    );
  }

  async createOnePaymentHistory(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;

    const paymentHistoryDetails = req.body as IPaymentHistoryAttributes;

    const paymentHistoryCreated = await this.paymentHistoryService.createOnePaymentHistory(
      orgId,
      employee.id,
      paymentHistoryDetails
    );

    // await this.paymentHistoryService.clearCachedData(organizationId);
    return sendJSONResponse(
      res,
      RESPONSES.paymentHistoryCreated,
      USER_ACTIONS.createPaymentHistory,
      paymentHistoryCreated
    );
  }

  async editOnePaymentHistory(req: Request, res: Response) {
    const orgId = res.locals.orgId as string;
    const employee = res.locals.employee as IEmployeeAttributes;
    const paymentHistoryId = req.params.id;
    const historyEdits = req.body as Partial<IPaymentHistoryAttributes>;

    const updatedHistory = await this.paymentHistoryService.editOnePaymentHistory(
      orgId,
      employee.id,
      paymentHistoryId,
      historyEdits
    );

    // await this.paymentHistoryService.clearCachedData(organizationId);
    return sendJSONResponse(
      res,
      RESPONSES.paymentHistoryUpdated,
      USER_ACTIONS.editPaymentHistory,
      updatedHistory
    );
  }
}
