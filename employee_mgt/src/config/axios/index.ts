import axios, { AxiosResponse, AxiosError } from 'axios';
import { axiosLogger } from '../../utilities/logger';
import { DEFINED_MS_ERROR_CODES_ARRAY } from '../../constants/values.constants';
import { createAppError, GatewayError, InternalServerError } from '../../helpers/error.helpers';

const axiosInstance = axios.create();

axiosInstance.interceptors.request.use(
  async (config) => {
    // log the request details with a redacted auth.
    axiosLogger.info({
      name: 'Axios Request',
      method: config.method,
      url: `${config.baseURL ?? ''}${config.url}`,
      parameters: config.params,
      headers: { ...config.headers, Authorization: '***' },
      timeStamp: new Date().toISOString(),
    });

    return config;
  },
  (error: AxiosError) => {
    // handle request setup error
    axiosLogger.error({
      name: 'Axios Request Config Setup Error',
      message: error?.message,
      cause: error?.cause,
      stack: error?.stack,
      error,
    });

    return Promise.reject(new InternalServerError());
  }
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,

  (error: AxiosError) => {
    //if request is successful and there is a response but an error occurred.
    if (error.response) {
      const { response } = error;

      const statusCode = response.status;
      const data = response?.data as any;
      const message = response.statusText;

      axiosLogger.error({
        name: 'Upstream Server Error',
        url: error?.config?.url,
        message,
        statusCode,
        data,
      });

      // handling our know api errors
      if (data && data?.code && DEFINED_MS_ERROR_CODES_ARRAY.includes(data.code)) {
        return Promise.reject(createAppError([data.message, statusCode], 'Upstream Server Error'));
      }

      // if the error is from bad request sent from the server
      if (String(statusCode).startsWith('4')) {
        return Promise.reject(new InternalServerError());
      }

      // return other errors as bad gateway error
      return Promise.reject(new GatewayError());
    }

    // if request is sent but no response is received
    if (error.request && !error.response) {
      axiosLogger.error({
        name: 'No Upstream Server Response Error',
        message: error?.message,
        url: error?.config?.url,
        code: error?.code,
        cause: error?.cause,
        error,
      });

      // request time out error
      if (error.code && error.code === 'ECONNABORTED') {
        return Promise.reject(new GatewayError('request timed out, please try again.'));
      }

      // network error
      if (
        (error.code && (error.code === 'ENOTFOUND' || error.code === 'ERR_NETWORK')) ||
        (error.message && error.message === 'Network Error')
      ) {
        return Promise.reject(
          new GatewayError('network error or DNS failure. please try again later.')
        );
      }

      // cannot connect with upstream server error
      if (error.code && error.code === 'ECONNREFUSED') {
        return Promise.reject(new GatewayError());
      }
      //default fall backs
      return Promise.reject(new GatewayError());
    }

    // fall back for unhandled errors
    axiosLogger.error({
      name: 'Unhandled Axios Error',
      message: error.message,
      url: error?.config?.url,
      error,
    });

    return Promise.reject(new InternalServerError());
  }
);

export default axiosInstance;
