import {
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_TYPE_ARRAY,
  GENDER_ARRAY,
  LEAVE_APPROVAL_STATUS_ARRAY,
  LEAVE_STATUS_ARRAY,
} from '../models/enums';

export const ERRORS = {
  notPermittedError: 'You are not authorized for this action.',
  serverError: 'internal server error',
  gatewayError: 'error connecting to upstream server',
  serviceUnavailableError: 'service unavailable, please try again later',
  rateLimitExceededError: 'rate limit exceeded, please try again later',
  fileDownloadingError: 'error downloading file, please try again later',
  recordNotFound: 'record not found',

  notAuthenticatedError: 'You are not authenticated',
  employeeExists: 'employee already exists',
  employeeNotFound: 'employee not found',
  employeeStatusConflict: 'employee status is same as new status',
  employeeAlreadyInvited: 'employee has already been invited',
  invalidLeaveId: 'leave ID is required and must be a valid UUID string',
  invalidEmployeeId: 'employee ID is required and must be a valid UUID string',

  invalidPaymentHistoryId: 'payment history ID is required and must be a valid UUID string',

  invalidPensionId: 'pension ID is required and must be a valid UUID string',

  invalidLeaveStatus: `leave status is required and must be one of: ${LEAVE_STATUS_ARRAY.join(' or ')}`,

  invalidLeaveApprovalStatus: `invalid leave approval status. must be one of: ${LEAVE_APPROVAL_STATUS_ARRAY.join(' or ')}`,
  invalidDeductibleId: 'deductible ID is required and must be a valid UUID string',
  invalidDeductibleIdsForUpdate: 'deductible with Id(s): (<IDs>) are not found',

  invalidEmploymentStatus: `employment status is invalid, valid statuses are: ${EMPLOYMENT_STATUS_ARRAY.join(' or ')}`,
  invalidGender: `gender is invalid, valid genders are: ${GENDER_ARRAY.join(' or ')}`,
  invalidEmploymentType: `invalid employment type. must be one of: ${EMPLOYMENT_TYPE_ARRAY.join(', ')}`,
  noEmployeeIdInQuery: 'employee ID is required in the query parameters',

  leaveNotFound: 'leave not found',
  leaveIsCompleted: 'leave is already completed',
  leaveIsRejected: 'leave is already rejected',

  leaveStatusConflict: 'leave status is same as new status',
  leaveIsAwaitingApproval: 'leave has not been approved or rejected',
  leaveIsCancelled: 'leave is already cancelled',
  leaveStartDateIsPast: 'cannot create a leave with past date',
  leaveAlreadyInProgress: `leave already in progress`,
  leaveIsNotAwaitingApproval: 'leave is not awaiting approval or rejection',

  leaveCannotBeUpdatedWithLesserEndDate:
    'cannot update leave with end date lesser than the start date',

  leaveStartDateCannotBeChanged:
    'leave start date cannot be changed for a leave that is in progress',

  leaveCannotBeEdited: 'leave cannot be edited after approval or rejection',

  nonEditableLeave: 'leave is <STATUS>, status cannot be edited',

  oneEmployeeSearchParamIsRequired:
    'at least one search parameter is required, valid params are: employment_status, role, first_name, last_name, gender, employment_type or email',

  paymentHistoryNotFound: 'payment history not found',
  deductibleNotFound: 'deductible not found',
  pensionNotFound: 'pension(s) not found',
  noOrganizationError: 'organization details is required.',
  noSubscriptionDetailsError:
    'subscription details not found, if problem persists, kindly reach out to support.',

  requiresActiveSubscriptionError:
    'your subscription has expired, kindly subscribe to a plan and try again.',
};
