import {
  BONUS_INTERVAL_ARRAY,
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_TYPE_ARRAY,
  EMPLOYMENT_TYPE_ENUM,
  GENDER_ARRAY,
  KIND_OF_PAYMENT_ARRAY,
  MODE_OF_PAYMENT_ARRAY,
  MODE_OF_PAYMENT_ENUM,
} from '../models/enums';
import { isDevelopmentEnv, isTestEnv } from '../utilities/guards';

export const API_VERSION = `/api/v1`;

export const HUNDRED = 100;
export const BASE_10 = 10;
export const ZERO = 0;
export const OTP_EXPIRE_TIME: number = 15;

export const FILE_EXTENSION = {
  PDF: 'pdf',
  JPEG: 'jpeg',
  JPG: 'jpg',
  PNG: 'png',
};

export const DGT_AUTH_TYPE = {
  KEY: 'dgtauth',
};
export const DGT_AUTH_TYPE_ARRAY = Object.values(DGT_AUTH_TYPE);

const BASE_ORIGINS = ['https://digit-tally.io'];

export const HTTP_METHODS = {
  POST: 'POST',
  GET: 'GET',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const DEV_ORIGINS = [
  'http://localhost:9628',
  'http://127.0.0.1:9628',
  'http://localhost:8752',
  'http://127.0.0.1:8752',
  'http://localhost:8756',
  'http://127.0.0.1:8756',
  'http://localhost:8759',
  'http://127.0.0.1:8759',
  'http://localhost:3932',
  'http://127.0.0.1:3932',
  'http://localhost:4512',
  'http://127.0.0.1:4512',
  'https://localhost:9628',
  'https://127.0.0.1:9628',
  'https://localhost:8752',
  'https://127.0.0.1:8752',
  'https://localhost:3932',
  'https://127.0.0.1:3932',
  'https://localhost:4512',
  'https://127.0.0.1:4512',
  'http://***********:8080',
  'http://*************:3000',
  'http://*************:3000',
  'http://*************:3000',
  'https://stgsvr008.digit-tally.io',
];

export const APP_ORIGINS = {
  DEV_USER_APP: process.env.DEV_USER_APP,
  DEV_ADMIN: process.env.DEV_ADMIN_APP,
  PROD_USER_APP: process.env.PROD_USER_APP,
  PROD_ADMIN: process.env.PROD_ADMIN_APP,
};

export const ALLOWED_ORIGINS =
  isDevelopmentEnv || isTestEnv
    ? [...DEV_ORIGINS, ...BASE_ORIGINS, process.env.DEV_USER_APP, process.env.DEV_ADMIN_APP]
    : [...BASE_ORIGINS, process.env.PROD_USER_APP, process.env.PROD_ADMIN_APP];

export const ADMIN_ORIGINS = [process.env.PROD_ADMIN_APP, process.env.DEV_ADMIN_APP];

export const USER_APP_ORIGINS =
  isDevelopmentEnv || isTestEnv
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP]
    : [APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP];

export const ADMIN_APP_ORIGINS =
  isDevelopmentEnv || isTestEnv
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN]
    : [APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN];

export const SERVICES = {
  employees: 'employee',
  leaves: 'leave',
  paymentHistories: 'paymentHistory',
  pensions: 'pension',
  deductibles: 'deductible',
};

export const TIME_IN_SECONDS = {
  fifteenMinutes: 15 * 60,
};

export const CREATE_EMPLOYEE_TEMPLATE = [
  {
    'S/N': 1,
    'Column Name': 'first_name',
    'Example Value': 'John',
    'Constraints/Validation Rule(s)': 'Required',
  },
  {
    'S/N': 2,
    'Column Name': 'middle_name',
    'Example Value': 'A.',
    'Constraints/Validation Rule(s)': 'Optional',
  },
  {
    'S/N': 3,
    'Column Name': 'last_name',
    'Example Value': 'Doe',
    'Constraints/Validation Rule(s)': 'Required',
  },
  {
    'S/N': 4,
    'Column Name': 'gender',
    'Example Value': 'Male',
    'Constraints/Validation Rule(s)': 'Required, accepted values are: ' + GENDER_ARRAY.join(' OR '),
  },
  {
    'S/N': 5,
    'Column Name': 'date_of_birth',
    'Example Value': '1990-05-15',
    'Constraints/Validation Rule(s)': 'Required, Format: YYYY-MM-DD',
  },
  {
    'S/N': 6,
    'Column Name': 'location',
    'Example Value': 'Lagos, Nigeria',
    'Constraints/Validation Rule(s)': 'Required',
  },
  {
    'S/N': 7,
    'Column Name': 'home_address',
    'Example Value': '12 Example Street, Lagos',
    'Constraints/Validation Rule(s)': 'Required',
  },
  {
    'S/N': 8,
    'Column Name': 'email',
    'Example Value': '<EMAIL>',
    'Constraints/Validation Rule(s)': 'Required, Must be a valid email',
  },
  {
    'S/N': 9,
    'Column Name': 'phone_number',
    'Example Value': '2348012345678',
    'Constraints/Validation Rule(s)': 'Required, Must be a valid phone number',
  },
  {
    'S/N': 10,
    'Column Name': 'emergency_number',
    'Example Value': '2348098765432',
    'Constraints/Validation Rule(s)': 'Optional, Must be a valid phone number',
  },
  {
    'S/N': 11,
    'Column Name': 'national_id',
    'Example Value': 'A123456789',
    'Constraints/Validation Rule(s)': 'Optional',
  },
  {
    'S/N': 12,
    'Column Name': 'employment_type',
    'Example Value': 'Full-time',
    'Constraints/Validation Rule(s)':
      'Required, Accepted values are: ' + EMPLOYMENT_TYPE_ARRAY.join(' OR '),
  },
  {
    'S/N': 13,
    'Column Name': 'role',
    'Example Value': 'Software Engineer',
    'Constraints/Validation Rule(s)': 'Required',
  },
  {
    'S/N': 14,
    'Column Name': 'employee_id',
    'Example Value': 'EMP12345',
    'Constraints/Validation Rule(s)': 'Optional',
  },
  {
    'S/N': 15,
    'Column Name': 'employment_start_date',
    'Example Value': '2022-01-10',
    'Constraints/Validation Rule(s)': 'Required, Format: YYYY-MM-DD',
  },
  {
    'S/N': 16,
    'Column Name': 'employment_end_date',
    'Example Value': '2025-01-10',
    'Constraints/Validation Rule(s)':
      'Optional, Required if employment type is ' + EMPLOYMENT_TYPE_ENUM.CONTRACT,
  },
  {
    'S/N': 17,
    'Column Name': 'kind_of_payment',
    'Example Value': 'Salary',
    'Constraints/Validation Rule(s)':
      'Required, Accepted values are: ' + KIND_OF_PAYMENT_ARRAY.join(' OR '),
  },
  {
    'S/N': 18,
    'Column Name': 'mode_of_payment',
    'Example Value': 'Bank Transfer',
    'Constraints/Validation Rule(s)':
      'Required, Accepted values are: ' + MODE_OF_PAYMENT_ARRAY.join(' OR '),
  },
  {
    'S/N': 19,
    'Column Name': 'salary',
    'Example Value': '500000',
    'Constraints/Validation Rule(s)': "Required if kind_of_payment is 'Salary'",
  },
  {
    'S/N': 20,
    'Column Name': 'hourly_rate',
    'Example Value': '5000',
    'Constraints/Validation Rule(s)':
      "Required if kind_of_payment is 'Hourly', Not needed for Salary. It must be present with work hours per week.",
  },
  {
    'S/N': 21,
    'Column Name': 'work_hours_per_week',
    'Example Value': '40',
    'Constraints/Validation Rule(s)':
      "Required if kind_of_payment is 'Hourly', Not needed for Salary. It must be present with hourly rate",
  },
  {
    'S/N': 22,
    'Column Name': 'bonus_percent',
    'Example Value': '10',
    'Constraints/Validation Rule(s)':
      'Percentage(e.g 10, 5) of salary as a bonus, input 0 if not applicable. do not add % sign',
  },
  {
    'S/N': 23,
    'Column Name': 'bonus_interval',
    'Example Value': 'Monthly',
    'Constraints/Validation Rule(s)':
      'Optional, Accepted values are: ' + BONUS_INTERVAL_ARRAY.join(' OR '),
  },
  {
    'S/N': 24,
    'Column Name': 'bank_name',
    'Example Value': 'GTBank',
    'Constraints/Validation Rule(s)':
      'Required if mode of payment is ' +
      MODE_OF_PAYMENT_ENUM.ELECTRONIC +
      ' and or any bank detail is provided',
  },
  {
    'S/N': 25,
    'Column Name': 'bank_account_name',
    'Example Value': 'John A. Doe',
    'Constraints/Validation Rule(s)':
      'Required if mode of payment is ' +
      MODE_OF_PAYMENT_ENUM.ELECTRONIC +
      ' and or any bank detail is provided',
  },
  {
    'S/N': 26,
    'Column Name': 'bank_account_number',
    'Example Value': '**********',
    'Constraints/Validation Rule(s)':
      'Required if mode of payment is ' +
      MODE_OF_PAYMENT_ENUM.ELECTRONIC +
      ' and or any bank detail is provided',
  },
  {
    'S/N': 27,
    'Column Name': 'tax_number',
    'Example Value': 'TAX123456',
    'Constraints/Validation Rule(s)': 'Required',
  },
  {
    'S/N': 28,
    'Column Name': 'tax_code',
    'Example Value': 'TX-987',
    'Constraints/Validation Rule(s)': 'Required',
  },
  {
    'S/N': 29,
    'Column Name': 'tax_rate',
    'Example Value': 7.5,
    'Constraints/Validation Rule(s)': 'Required, Percentage value. Do not add the % sign',
  },
  {
    'S/N': 30,
    'Column Name': 'employment_status',
    'Example Value': 'active',
    'Constraints/Validation Rule(s)':
      'Required, Accepted values are: ' + EMPLOYMENT_STATUS_ARRAY.join(' OR '),
  },
  {
    'S/N': 31,
    'Column Name': 'pension_provider',
    'Example Value': 'Pension Funds',
    'Constraints/Validation Rule(s)': "Required if employment_type is 'Full-time'",
  },
  {
    'S/N': 32,
    'Column Name': 'pension_start_date',
    'Example Value': '2023-01-01',
    'Constraints/Validation Rule(s)': 'Optional, Format: YYYY-MM-DD',
  },
  {
    'S/N': 33,
    'Column Name': 'pension_monthly_contribution',
    'Example Value': 20000,
    'Constraints/Validation Rule(s)': 'Optional, Required if pension_provider is provided',
  },
  {
    'S/N': 34,
    'Column Name': 'pension_beneficiary_first_name',
    'Example Value': 'Jane',
    'Constraints/Validation Rule(s)': 'Optional, Required if pension is provided',
  },
  {
    'S/N': 35,
    'Column Name': 'pension_beneficiary_middle_name',
    'Example Value': 'B.',
    'Constraints/Validation Rule(s)': 'Optional',
  },
  {
    'S/N': 36,
    'Column Name': 'pension_beneficiary_last_name',
    'Example Value': 'Doe',
    'Constraints/Validation Rule(s)': 'Optional, Required if pension is provided',
  },
  {
    'S/N': 37,
    'Column Name': 'pension_beneficiary_phone_number',
    'Example Value': '2348098765432',
    'Constraints/Validation Rule(s)': 'Optional, Must be a valid phone number',
  },
  {
    'S/N': 38,
    'Column Name': 'pension_beneficiary_relation',
    'Example Value': 'Spouse',
    'Constraints/Validation Rule(s)': 'Optional, Required if pension is provided',
  },
  {
    'S/N': 39,
    'Column Name': 'pension_beneficiary_date_of_birth',
    'Example Value': '1995-07-20',
    'Constraints/Validation Rule(s)': 'Optional, Format: YYYY-MM-DD',
  },
];

export const ROUTE_IDS = {
  organizationId: 'organizationId',
  employeeId: 'employeeId',
  leaveId: 'leaveId',
  deductibleId: 'deductibleId',
  pensionId: 'pensionId',
  paymentId: 'paymentId',
};

export const USER_ACTIONS = {
  // Employee actions
  createEmployee: 'create employee',
  createBulkEmployees: 'create bulk employees',
  downloadMultipleEmployeeUploadTemplate: 'download create bulk employees template',
  exportEmployeeData: 'export employee data',
  inviteEmployee: 'invite employee',
  editEmployee: 'edit employee',
  editEmployeeStatus: 'edit employee status',
  getEmployee: 'get employee(s)',
  deleteEmployee: 'delete employee',
  searchEmployee: 'search employee(s)',

  // Deductible actions
  createDeductible: 'create deductible',
  editDeductible: 'edit deductible',
  getDeductible: 'get deductible(s)',
  deleteDeductible: 'delete deductible',
  searchDeductible: 'search deductible(s)',

  // Pension actions
  createPension: 'create pension',
  editPension: 'edit pension',
  getPension: 'get pension(s)',
  deletePension: 'delete pension',
  searchPension: 'search pension(s)',

  // Leave actions
  createLeave: 'create leave',
  editLeave: 'edit leave',
  getLeave: 'get leave(s)',
  deleteLeave: 'delete leave',
  searchLeave: 'search leave(s)',

  // Payment history actions
  createPaymentHistory: 'create payment history',
  editPaymentHistory: 'edit payment history(ies)',
  getPaymentHistory: 'get payment history(ies)',
  searchPaymentHistory: 'search payment history(ies)',
  downloadPaymentHistory: 'download payment history',
  deletePaymentHistory: 'delete payment history',

  // Utility actions (unchanged)
  getServerHealth: 'get server health',
  getDocumentation: 'get documentation',
  accessNonAvailableResource: 'access non available resources',
};

export const DEFINED_MS_ERROR_CODES_WITH_MESSAGES = {
  400: 'EB400',
  401: 'EA401',
  403: 'EP403',
  404: 'EN404',
  409: 'EC409',
  500: 'ES500',
} as const;

export const DEFINED_MS_ERROR_CODES_ARRAY = Object.values(DEFINED_MS_ERROR_CODES_WITH_MESSAGES);

export const NOTIFICATION_EVENT_NAMES = {
  app: 'app.notifications',
  admin: 'admin.notifications',
} as const;

export const COUNTRY_NAMES_AND_TWO_LETTER_CODES = {
  afghanistan: 'AF',
  albania: 'AL',
  algeria: 'DZ',
  'american samoa': 'AS',
  andorra: 'AD',
  angola: 'AO',
  anguilla: 'AI',
  antarctica: 'AQ',
  'antigua and barbuda': 'AG',
  argentina: 'AR',
  armenia: 'AM',
  aruba: 'AW',
  australia: 'AU',
  austria: 'AT',
  azerbaijan: 'AZ',
  bahamas: 'BS',
  bahrain: 'BH',
  bangladesh: 'BD',
  barbados: 'BB',
  belarus: 'BY',
  belgium: 'BE',
  belize: 'BZ',
  benin: 'BJ',
  bermuda: 'BM',
  bhutan: 'BT',
  bolivia: 'BO',
  'bosnia and herzegovina': 'BA',
  botswana: 'BW',
  brazil: 'BR',
  brunei: 'BN',
  bulgaria: 'BG',
  'burkina faso': 'BF',
  burundi: 'BI',
  cambodia: 'KH',
  cameroon: 'CM',
  canada: 'CA',
  'cape verde': 'CV',
  'central african republic': 'CF',
  chad: 'TD',
  chile: 'CL',
  china: 'CN',
  colombia: 'CO',
  comoros: 'KM',
  'congo (brazzaville)': 'CG',
  'congo (kinshasa)': 'CD',
  'costa rica': 'CR',
  croatia: 'HR',
  cuba: 'CU',
  cyprus: 'CY',
  'czech republic': 'CZ',
  denmark: 'DK',
  djibouti: 'DJ',
  dominica: 'DM',
  'dominican republic': 'DO',
  ecuador: 'EC',
  egypt: 'EG',
  'el salvador': 'SV',
  'equatorial guinea': 'GQ',
  eritrea: 'ER',
  estonia: 'EE',
  eswatini: 'SZ',
  ethiopia: 'ET',
  fiji: 'FJ',
  finland: 'FI',
  france: 'FR',
  gabon: 'GA',
  gambia: 'GM',
  georgia: 'GE',
  germany: 'DE',
  ghana: 'GH',
  greece: 'GR',
  greenland: 'GL',
  grenada: 'GD',
  guatemala: 'GT',
  guinea: 'GN',
  'guinea-bissau': 'GW',
  guyana: 'GY',
  haiti: 'HT',
  honduras: 'HN',
  'hong kong': 'HK',
  hungary: 'HU',
  iceland: 'IS',
  india: 'IN',
  indonesia: 'ID',
  iran: 'IR',
  iraq: 'IQ',
  ireland: 'IE',
  israel: 'IL',
  italy: 'IT',
  jamaica: 'JM',
  japan: 'JP',
  jordan: 'JO',
  kazakhstan: 'KZ',
  kenya: 'KE',
  kiribati: 'KI',
  kuwait: 'KW',
  kyrgyzstan: 'KG',
  laos: 'LA',
  latvia: 'LV',
  lebanon: 'LB',
  lesotho: 'LS',
  liberia: 'LR',
  libya: 'LY',
  liechtenstein: 'LI',
  lithuania: 'LT',
  luxembourg: 'LU',
  madagascar: 'MG',
  malawi: 'MW',
  malaysia: 'MY',
  maldives: 'MV',
  mali: 'ML',
  malta: 'MT',
  mauritania: 'MR',
  mauritius: 'MU',
  mexico: 'MX',
  moldova: 'MD',
  monaco: 'MC',
  mongolia: 'MN',
  montenegro: 'ME',
  morocco: 'MA',
  mozambique: 'MZ',
  myanmar: 'MM',
  namibia: 'NA',
  nepal: 'NP',
  netherlands: 'NL',
  'new zealand': 'NZ',
  nicaragua: 'NI',
  niger: 'NE',
  nigeria: 'NG',
  'north korea': 'KP',
  'north macedonia': 'MK',
  norway: 'NO',
  oman: 'OM',
  pakistan: 'PK',
  palestine: 'PS',
  panama: 'PA',
  'papua new guinea': 'PG',
  paraguay: 'PY',
  peru: 'PE',
  philippines: 'PH',
  poland: 'PL',
  portugal: 'PT',
  qatar: 'QA',
  romania: 'RO',
  russia: 'RU',
  rwanda: 'RW',
  'saudi arabia': 'SA',
  senegal: 'SN',
  serbia: 'RS',
  seychelles: 'SC',
  'sierra leone': 'SL',
  singapore: 'SG',
  slovakia: 'SK',
  slovenia: 'SI',
  'solomon islands': 'SB',
  somalia: 'SO',
  'south africa': 'ZA',
  'south korea': 'KR',
  'south sudan': 'SS',
  spain: 'ES',
  'sri lanka': 'LK',
  sudan: 'SD',
  suriname: 'SR',
  sweden: 'SE',
  switzerland: 'CH',
  syria: 'SY',
  taiwan: 'TW',
  tajikistan: 'TJ',
  tanzania: 'TZ',
  thailand: 'TH',
  togo: 'TG',
  tonga: 'TO',
  'trinidad and tobago': 'TT',
  tunisia: 'TN',
  turkey: 'TR',
  turkmenistan: 'TM',
  uganda: 'UG',
  ukraine: 'UA',
  'united arab emirates': 'AE',
  'united kingdom': 'GB',
  'united states': 'US',
  uruguay: 'UY',
  uzbekistan: 'UZ',
  vanuatu: 'VU',
  venezuela: 'VE',
  vietnam: 'VN',
  yemen: 'YE',
  zambia: 'ZM',
  zimbabwe: 'ZW',
} as const;

export const COUNTRY_NAMES = Object.keys(COUNTRY_NAMES_AND_TWO_LETTER_CODES);

export const EMPLOYEES_CREATION_UNIQUE_FIELDS = [
  'emails',
  'phoneNumbers',
  'nationalIds',
  'taxNumbers',
  'taxCodes',
] as const;

export const EMPLOYEE_CREATION_UNIQUE_FIELD_TO_DB_COLUMN_NAME_MAP = {
  emails: 'email',
  phoneNumbers: 'phone_number',
  nationalIds: 'national_id',
  taxNumbers: 'tax_number',
  taxCodes: 'tax_code',
} as const;
