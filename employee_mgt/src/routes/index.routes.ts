import { Router } from 'express';
import { API_VERSION } from '../constants/values.constants';
import utilityRouter from './utilities.routes';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';
import orgRouter from './organization/index.routes';
import empRouter from './employee/index.routes';
import adminRouter from './super_admin/index.routes';
import { RateLimiters } from '../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../utilities/guards';

const globalRouter = Router({ mergeParams: true });

const { captureAppDetails } = middlewares.resolve('utilityMiddleware');
globalRouter.use(captureAppDetails);

if (isProductionEnv) {
  globalRouter.use(RateLimiters.global);
}

globalRouter.use(`${API_VERSION}/employees`, utilityRouter);
globalRouter.use(`${API_VERSION}/admin/organizations/:organizationId/employees`, adminRouter);
globalRouter.use(`${API_VERSION}/employees`, orgRouter);
globalRouter.use(`${API_VERSION}/employee/me`, empRouter);

globalRouter.all('*', controllers.resolve('utilityController').resourceNotFound);

export default globalRouter;
