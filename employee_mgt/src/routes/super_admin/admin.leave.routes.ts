import {
  validateQueryParams,
  validateRequestBody,
  validateRouteIdParam,
} from '../../middlewares/validators/global.validators';

import { instantiateRouter } from '../../helpers/routes.helpers';
import { ROUTE_IDS } from '../../constants/values.constants';
import {
  createLeaveSchema,
  editLeaveSchema,
} from '../../middlewares/validators/schemas/request_body/leave.schema';
import { approveOrRejectLeaveQuerySchema } from '../../middlewares/validators/schemas/query_params/leaves.query.params.schemas';

const {
  router: adminLeaveRouter,
  controller: leaveControllers,
  // cache,
  // service: leave,
} = instantiateRouter('employeeLeaveController', 'leaves');

// Get all leaves and create a leave
adminLeaveRouter
  .route('/')
  .get(leaveControllers.getAllLeaves)
  .post(validateRequestBody(createLeaveSchema), leaveControllers.createOneLeave);

const leaveId = ROUTE_IDS.leaveId;

// Leave approval or rejection
adminLeaveRouter
  .route(`/:${leaveId}/approve-or-reject`)
  .all(validateRouteIdParam(leaveId))
  .patch(
    validateQueryParams(approveOrRejectLeaveQuerySchema),
    leaveControllers.approveOrRejectLeave
  );

// Leave status update
adminLeaveRouter
  .route(`/:${leaveId}/cancel`)
  .all(validateRouteIdParam(leaveId))
  .patch(leaveControllers.cancelLeave);

// Leave details and update
adminLeaveRouter
  .route(`/:${leaveId}`)
  .all(validateRouteIdParam(leaveId))
  .get(leaveControllers.getOneLeave)
  .put(validateRequestBody(editLeaveSchema), leaveControllers.editOneLeave);

export default adminLeaveRouter;
