import { Router } from 'express';
import middlewares from '../../containers/middlewares.container';
import { validateRouteIdParam } from '../../middlewares/validators/global.validators';
import { ROUTE_IDS } from '../../constants/values.constants';
import adminEmployeeRouter from './admin.employee.routes';
import adminLeaveRouter from './admin.leave.routes';
import { RateLimiters } from '../../middlewares/utils/rate-limiter.middleware';
import { isProductionEnv } from '../../utilities/guards';

const adminRouter = Router({ mergeParams: true });

if (isProductionEnv) {
  adminRouter.use(RateLimiters.adminRequest);
}

const auth = middlewares.resolve('authMiddleware');

adminRouter.use(
  auth.authenticateUser.bind(auth),
  auth.restrictToAdminAccess.bind(auth),
  validateRouteIdParam(ROUTE_IDS.organizationId),
  middlewares.resolve('utilityMiddleware').extractOrgIdAndMembersFromUser
);

adminRouter.use('/', adminEmployeeRouter);
adminRouter.use('/leaves', adminLeaveRouter);

export default adminRouter;
