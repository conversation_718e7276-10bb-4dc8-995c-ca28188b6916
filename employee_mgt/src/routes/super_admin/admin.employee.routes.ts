import { ROUTE_IDS } from '../../constants/values.constants';
import { instantiateRouter } from '../../helpers/routes.helpers';
import * as validators from '../../middlewares/validators/global.validators';
import {
  employeeStatusQuerySchema,
  searchEmployeeQuerySchema,
} from '../../middlewares/validators/schemas/query_params/employees.query.params.schema';
import {
  createEmployeeSchema,
  editEmployeeSchema,
} from '../../middlewares/validators/schemas/request_body/employee.schema';

const {
  router: adminEmployeeRouter,
  controller: employeeControllers,
  // cache,
  // service: employee,
} = instantiateRouter('employeeController', 'employees');

const {
  validateQueryParams,
  validateRequestBody,
  validateRouteIdParam: validateRouteIdParameter,
} = validators;

// Search route
adminEmployeeRouter
  .route('/search')
  .get(validateQueryParams(searchEmployeeQuerySchema), employeeControllers.searchForEmployees);

// // Bulk upload routes
// superAdminEmployeeRouter
//   .route('/bulk-upload/template')
//   .get(employeeControllers.downloadBulkUploadTemplate);

// superAdminEmployeeRouter
//   .route('/bulk-upload')
//   .post(
//     validatePayload(createBulkEmployeeSchema),
//     employeeControllers.createBulkEmployee
//   );

const employeeId = ROUTE_IDS.employeeId;

// Sub-routes related to employee-specific actions
adminEmployeeRouter
  .route(`/:${employeeId}/status`)
  .all(validateRouteIdParameter(employeeId))
  .patch(
    validateQueryParams(employeeStatusQuerySchema),
    employeeControllers.changeEmployeeEmploymentStatus
  );

adminEmployeeRouter
  .route(`/:${employeeId}/invite-employee`)
  .all(validateRouteIdParameter(employeeId))
  .post(employeeControllers.inviteEmployee);

// Standard CRUD for employee by ID
adminEmployeeRouter
  .route(`/:${employeeId}`)
  .all(validateRouteIdParameter(employeeId))
  .get(employeeControllers.getOneEmployee)
  .put(validateRequestBody(editEmployeeSchema), employeeControllers.editOneEmployee);

// Base employee routes
adminEmployeeRouter
  .route('/')
  .get(employeeControllers.getAllEmployees)
  .post(validateRequestBody(createEmployeeSchema), employeeControllers.createOneEmployee);
export default adminEmployeeRouter;
