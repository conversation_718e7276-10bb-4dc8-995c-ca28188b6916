import middlewares from '../../containers/middlewares.container';
import { instantiateRouter } from '../../helpers/routes.helpers';
import { RateLimiters } from '../../middlewares/utils/rate-limiter.middleware';
import {
  validateQueryParams,
  validateRequestBody,
  validateRouteIdParam,
} from '../../middlewares/validators/global.validators';
import { APP_ROLES } from '../../models/enums';
import {
  createMultipleEmployeeSchema,
  createEmployeeSchema,
} from '../../middlewares/validators/schemas/request_body/employee.schema';
import { isProductionEnv } from '../../utilities/guards';
import specificEmployeeRouter from './specific_employee/index.routes';
import { searchEmployeeQuerySchema } from '../../middlewares/validators/schemas/query_params/employees.query.params.schema';

const { router: orgRouter, controller: employeeControllers } =
  instantiateRouter('employeeController');

if (isProductionEnv) {
  orgRouter.use(RateLimiters.organizationRequest);
}

const { authenticateUser, verifyPermission, validateActiveSubscription } =
  middlewares.resolve('authMiddleware');

const { extractOrgIdAndMembersFromUser } = middlewares.resolve('utilityMiddleware');

orgRouter.use(
  authenticateUser,
  verifyPermission([APP_ROLES.OWNER]),
  validateActiveSubscription,
  extractOrgIdAndMembersFromUser
);

const {
  searchForEmployees,
  downloadMultipleEmployeeUploadTemplate,
  createMultipleEmployees,
  getAllEmployees,
  createOneEmployee,
} = employeeControllers;

// Search employee
orgRouter.get('/search', validateQueryParams(searchEmployeeQuerySchema), searchForEmployees);

// get bulk upload employees template
orgRouter.get('/bulk-upload/template', downloadMultipleEmployeeUploadTemplate);

// bulk upload employees
orgRouter.post(
  '/bulk-upload',
  validateRequestBody(createMultipleEmployeeSchema),
  createMultipleEmployees
);

// get all or create an employee
orgRouter
  .route('/')
  .get(getAllEmployees)
  .post(validateRequestBody(createEmployeeSchema), createOneEmployee);

// employee specific actions
orgRouter.use('/:employeeId', validateRouteIdParam('employeeId'), specificEmployeeRouter);

export default orgRouter;
