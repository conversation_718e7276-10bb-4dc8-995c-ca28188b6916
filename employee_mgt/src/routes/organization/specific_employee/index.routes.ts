import { instantiateRouter } from '../../../helpers/routes.helpers';
import specificEmpLeavesRouter from './leave.routes';
import specificEmpPaymentsRouter from './payment-history.routes';
import specificEmpPensionsRouter from './pension.routes';
import specificEmpDeductiblesRouter from './deductible.routes';
import {
  validateQueryParams,
  validateRequestBody,
} from '../../../middlewares/validators/global.validators';
import { employeeStatusQuerySchema } from '../../../middlewares/validators/schemas/query_params/employees.query.params.schema';
import { editEmployeeSchema } from '../../../middlewares/validators/schemas/request_body/employee.schema';
import middlewares from '../../../containers/middlewares.container';

const { router: specificEmployeeRouter, controller: employeeControllers } =
  instantiateRouter('employeeController');

const { changeEmployeeEmploymentStatus, editOneEmployee, getOneEmployee } = employeeControllers;

specificEmployeeRouter.patch(
  '/status',
  validateQueryParams(employeeStatusQuerySchema),
  changeEmployeeEmploymentStatus
);

// orgEmployeeRouter.route(`/invite-employee`).post(employeeControllers.inviteEmployee);

// get one and update one employee
specificEmployeeRouter
  .route('/')
  .get(getOneEmployee)
  .put(validateRequestBody(editEmployeeSchema), editOneEmployee);

const { extractEmployeeFromRequest } = middlewares.resolve('utilityMiddleware');

specificEmployeeRouter.use(extractEmployeeFromRequest);

specificEmployeeRouter.use('/leaves', specificEmpLeavesRouter);
specificEmployeeRouter.use('/payments', specificEmpPaymentsRouter);
specificEmployeeRouter.use('/pensions', specificEmpPensionsRouter);
specificEmployeeRouter.use('/deductibles', specificEmpDeductiblesRouter);

export default specificEmployeeRouter;
