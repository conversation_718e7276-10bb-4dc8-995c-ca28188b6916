import { ROUTE_IDS } from '../../../constants/values.constants';
import { instantiateRouter } from '../../../helpers/routes.helpers';
import {
  createDeductibleSchema,
  editDeductibleSchema,
} from '../../../middlewares/validators/schemas/request_body/deductible.schema';
import {
  validateRequestBody,
  validateRouteIdParam,
} from '../../../middlewares/validators/global.validators';

const {
  router: specificEmpDeductiblesRouter,
  controller: EmployeeDeductibleController,
  // service: deductible,
  // cache,
} = instantiateRouter('employeeDeductibleController', 'deductibles');

const {
  createMultipleDeductibles: createDeductibles,
  getOneDeductible,
  editOneDeductible,
  getAllDeductibles,
} = EmployeeDeductibleController;

specificEmpDeductiblesRouter
  .route('/')
  .get(getAllDeductibles)
  .post(validateRequestBody(createDeductibleSchema), createDeductibles);

const deductibleId = ROUTE_IDS.deductibleId;

specificEmpDeductiblesRouter
  .route(`/:${deductibleId}`)
  .all(validateRouteIdParam(deductibleId))
  .get(getOneDeductible)
  .put(validateRequestBody(editDeductibleSchema), editOneDeductible);
// .delete(deductibleControllers.deleteDeductible);

export default specificEmpDeductiblesRouter;
