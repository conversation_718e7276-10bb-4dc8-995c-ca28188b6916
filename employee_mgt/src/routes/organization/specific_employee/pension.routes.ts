import { ROUTE_IDS } from '../../../constants/values.constants';
import { instantiateRouter } from '../../../helpers/routes.helpers';
import {
  createPensionSchema,
  editPensionSchema,
} from '../../../middlewares/validators/schemas/request_body/pension.schema';
import {
  validateRequestBody,
  validateRouteIdParam,
} from '../../../middlewares/validators/global.validators';

const {
  router: specificEmpPensionsRouter,
  controller: employeePensionControllers,
  // cache,
  // service: pension,
} = instantiateRouter('employeePensionController', 'pensions');

const { createOnePension, getPension, editOnePension } = employeePensionControllers;

specificEmpPensionsRouter
  .route('/')
  .get(getPension)
  .post(validateRequestBody(createPensionSchema), createOnePension);

const pensionId = ROUTE_IDS.pensionId;
specificEmpPensionsRouter.use(validateRouteIdParam(pensionId));

specificEmpPensionsRouter
  .route(`/:${pensionId}`)
  .put(validateRequestBody(editPensionSchema), editOnePension);
// .delete(validatePensionId, pensionControllers.DeletePension);

export default specificEmpPensionsRouter;
