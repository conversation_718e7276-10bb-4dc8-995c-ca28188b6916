import { ROUTE_IDS } from '../../../constants/values.constants';
import { instantiateRouter } from '../../../helpers/routes.helpers';
import { approveOrRejectLeaveQuerySchema } from '../../../middlewares/validators/schemas/query_params/leaves.query.params.schemas';
import {
  createLeaveSchema,
  editLeaveSchema,
} from '../../../middlewares/validators/schemas/request_body/leave.schema';
import {
  validateQueryParams,
  validateRequestBody,
  validateRouteIdParam,
} from '../../../middlewares/validators/global.validators';

const {
  router: specificEmpLeavesRouter,
  controller: employeeLeaveControllers,
  // cache,
  // service: leave,
} = instantiateRouter('employeeLeaveController', 'leaves');

const {
  getAllLeaves,
  approveOrRejectLeave,
  cancelLeave,
  getOneLeave,
  editOneLeave,
  createOneLeave,
} = employeeLeaveControllers;

specificEmpLeavesRouter
  .route('/')
  .get(getAllLeaves)
  .post(validateRequestBody(createLeaveSchema), createOneLeave);

const leaveId = ROUTE_IDS.leaveId;
specificEmpLeavesRouter.use(validateRouteIdParam(leaveId));

// Leave approval or rejection
specificEmpLeavesRouter.patch(
  `/:${leaveId}/approve-or-reject`,
  validateQueryParams(approveOrRejectLeaveQuerySchema),
  approveOrRejectLeave
);

// Leave cancel
specificEmpLeavesRouter.patch(`/:${leaveId}/cancel`, cancelLeave);

// Leave details and update
specificEmpLeavesRouter
  .route(`/:${leaveId}`)
  .get(getOneLeave)
  .put(validateRequestBody(editLeaveSchema), editOneLeave);

export default specificEmpLeavesRouter;
