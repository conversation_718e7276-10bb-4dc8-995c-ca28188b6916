import { ROUTE_IDS } from '../../../constants/values.constants';
import { instantiateRouter } from '../../../helpers/routes.helpers';
import { createPaymentHistorySchema } from '../../../middlewares/validators/schemas/request_body/payment-history.schema';
import {
  validateRequestBody,
  validateRouteIdParam,
} from '../../../middlewares/validators/global.validators';

const {
  router: specificEmpPaymentsRouter,
  controller: employeePaymentHistoryControllers,
  // cache,
  // service: paymentHistory,
} = instantiateRouter('employeePaymentHistoryController', 'paymentHistories');

const { getOnePaymentHistory, createOnePaymentHistory, getAllPaymentHistories } =
  employeePaymentHistoryControllers;

specificEmpPaymentsRouter
  .route('/')
  .get(getAllPaymentHistories)
  .post(validateRequestBody(createPaymentHistorySchema), createOnePaymentHistory);

const paymentId = ROUTE_IDS.paymentId;
specificEmpPaymentsRouter.use(validateRouteIdParam(paymentId));

specificEmpPaymentsRouter.get(`/:${paymentId}`, getOnePaymentHistory);

export default specificEmpPaymentsRouter;
