import {
  validateRequestBody,
  validateRouteIdParam,
} from '../../middlewares/validators/global.validators';
import { instantiateRouter } from '../../helpers/routes.helpers';
import { ROUTE_IDS } from '../../constants/values.constants';
import { createLeaveSchema } from '../../middlewares/validators/schemas/request_body/leave.schema';

const { router: empAccess<PERSON>eaveRouter, controller: leaveControllers } =
  instantiateRouter('employeeLeaveController');

const leaveId = ROUTE_IDS.leaveId;

empAccessLeaveRouter
  .route('/')
  .get(leaveControllers.getAllLeaves)
  .post(validateRequestBody(createLeaveSchema), leaveControllers.createOneLeave);

empAccessLeaveRouter
  .route(`/:${leaveId}`)
  .all(validateRouteIdParam(leaveId))
  .get(leaveControllers.getOneLeave);

export default empAccessLeaveRouter;
