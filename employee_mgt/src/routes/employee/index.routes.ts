import middlewares from '../../containers/middlewares.container';
import { APP_ROLES } from '../../models/enums';
import empAccessLeaveRouter from './emp.leave.routes';
import { instantiateRouter } from '../../helpers/routes.helpers';

const { router: empRouter, controller: employeeControllers } =
  instantiateRouter('employeeController');

const {
  authenticateUser: authProtect,
  verifyPermission,
  validateActiveSubscription,
} = middlewares.resolve('authMiddleware');

const { extractOrgIdAndMembersFromUser: extractOrgDetailsFromRequest } =
  middlewares.resolve('utilityMiddleware');

empRouter.use(authProtect, verifyPermission([APP_ROLES.EMPLOYEE]), validateActiveSubscription);

empRouter.use(extractOrgDetailsFromRequest);

empRouter.get('/', employeeControllers.getOneEmployee);
empRouter.use('/leaves', empAccessLeaveRouter);

export default empRouter;
