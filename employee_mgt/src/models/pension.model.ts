import { DataTypes, Model } from 'sequelize';
import { Models } from './associations.model';
import sequelize from '../config/database/connection';

export interface IPensionAttributes {
  id: string;
  organization_id: string;
  employee_id: string;
  provider: string;
  policy_number: string;
  start_date: Date;
  monthly_contribution: number;
  beneficiary_first_name: string;
  beneficiary_middle_name: string;
  beneficiary_last_name: string;
  beneficiary_phone_number: string;
  beneficiary_relation: string;
  beneficiary_date_of_birth: string;

  created_at?: Date;
  updated_at?: Date;
}

class Pension extends Model<IPensionAttributes> implements IPensionAttributes {
  declare id: string;
  declare organization_id: string;
  declare employee_id: string;
  declare provider: string;
  declare policy_number: string;
  declare start_date: Date;
  declare monthly_contribution: number;
  declare beneficiary_first_name: string;
  declare beneficiary_middle_name: string;
  declare beneficiary_last_name: string;
  declare beneficiary_phone_number: string;
  declare beneficiary_relation: string;
  declare beneficiary_date_of_birth: string;

  declare readonly created_at?: Date;
  declare readonly updated_at?: Date;

  static associate(models: Models) {
    Pension.belongsTo(models.Employee, {
      foreignKey: 'employee_id',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Pension.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
      unique: true,
      defaultValue: DataTypes.UUIDV4,
    },
    organization_id: {
      type: DataTypes.STRING,
      references: { model: 'organizations', key: 'id' },
      allowNull: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: { model: 'employees', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    provider: { type: DataTypes.STRING, allowNull: false },
    policy_number: { type: DataTypes.STRING },
    start_date: { type: DataTypes.DATE, allowNull: false },
    monthly_contribution: { type: DataTypes.FLOAT, allowNull: false },
    beneficiary_first_name: { type: DataTypes.STRING, allowNull: false },
    beneficiary_middle_name: { type: DataTypes.STRING, defaultValue: '' },
    beneficiary_last_name: { type: DataTypes.STRING, allowNull: false },
    beneficiary_phone_number: { type: DataTypes.STRING, allowNull: false },
    beneficiary_relation: { type: DataTypes.STRING, allowNull: false },
    beneficiary_date_of_birth: { type: DataTypes.DATE, allowNull: false },
  },
  {
    sequelize,
    modelName: 'Pension',
    tableName: 'employees_pensions',
    indexes: [
      {
        name: 'employees_pensions_idx',
        unique: true,
        fields: ['employee_id', 'provider', 'organization_id'],
      },
    ],
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default Pension;
