import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';
import { Models } from './associations.model';

export interface IPaymentHistoryAttributes {
  id: string;
  organization_id: string;
  employee_id: string;
  payment_date: Date;
  amount_paid: number;
  // payment_method: string;
  payment_status: string;

  created_at?: Date;
  updated_at?: Date;
}

class PaymentHistory extends Model<IPaymentHistoryAttributes> implements IPaymentHistoryAttributes {
  declare id: string;
  declare organization_id: string;
  declare employee_id: string;
  declare payment_date: Date;
  declare amount_paid: number;
  // declare payment_method: string;
  declare payment_status: string;

  declare readonly created_at?: Date;
  declare readonly updated_at?: Date;

  static associate(model: Models) {
    PaymentHistory.belongsTo(model.Employee, {
      foreignKey: 'employee_id',
      targetKey: 'id',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

PaymentHistory.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      unique: true,
      allowNull: false,
    },
    organization_id: {
      type: DataTypes.STRING,
      references: { model: 'organizations', key: 'id' },
      allowNull: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      references: { model: 'employees', key: 'id' },
      allowNull: false,
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    payment_date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    amount_paid: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    // payment_method: {
    //   type: DataTypes.STRING,
    //   allowNull: false,
    // },
    payment_status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'PaymentHistory',
    tableName: 'employees_payment_history',
    indexes: [
      {
        name: 'employees_payment_history_idx',
        unique: true,
        fields: ['employee_id', 'organization_id', 'payment_status'],
      },
    ],
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default PaymentHistory;
