import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database/connection';

import {
  BONUS_INTERVAL_ARRAY,
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_STATUS_ENUM,
  EMPLOYMENT_TYPE_ARRAY,
  GENDER_ARRAY,
  KIND_OF_PAYMENT_ARRAY,
  MODE_OF_PAYMENT_ARRAY,
} from './enums';
import { ILeaveAttributes } from './leave.model';
import { IPensionAttributes } from './pension.model';
import { IPaymentHistoryAttributes } from './payment-history.model';
import { Models } from './associations.model';
import { IDeductibleAttributes } from './deductible.model';

export interface IEmployeeAttributes {
  id: string;
  organization_id: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  gender: string;
  date_of_birth: Date;
  country: string;
  home_address: string;
  email: string;
  phone_number: string;
  emergency_number: string;
  national_id: string;
  employment_type: string;
  role: string;
  employee_id: string;
  employment_start_date: Date;
  employment_end_date: Date;
  kind_of_payment: string;
  mode_of_payment: string;
  salary: number;
  hourly_rate: number;
  work_hours_per_week: number;
  bonus_percent: number;
  bonus_interval: string;
  bank_name: string;
  bank_account_name: string;
  bank_account_number: string;
  tax_number: string;
  tax_code: string;
  tax_rate: number;
  employment_status: string;
  is_invited: boolean;
  // hash: string;
  pension?: IPensionAttributes;
  leaves?: ILeaveAttributes[];
  payment_histories?: IPaymentHistoryAttributes[];
  deductibles?: IDeductibleAttributes[];

  created_at?: Date;
  updated_at?: Date;
}

// interface Models {
//   User: typeof User;
//   StatutoryLeave: typeof StatutoryLeave;
//   Payments: typeof Payments;
//   Deductible: typeof Deductible;
// }

class Employee extends Model<IEmployeeAttributes> implements IEmployeeAttributes {
  declare id: string;
  declare organization_id: string;
  declare first_name: string;
  declare middle_name: string;
  declare last_name: string;
  declare gender: string;
  declare date_of_birth: Date;
  declare country: string;
  declare home_address: string;
  declare email: string;
  declare phone_number: string;
  declare emergency_number: string;
  declare national_id: string;
  declare employment_type: string;
  declare role: string;
  declare employee_id: string;
  declare employment_start_date: Date;
  declare employment_end_date: Date;
  declare kind_of_payment: string;
  declare mode_of_payment: string;
  declare salary: number;
  declare hourly_rate: number;
  declare work_hours_per_week: number;
  declare bonus_percent: number;
  declare bonus_interval: string;
  declare bank_name: string;
  declare bank_account_name: string;
  declare bank_account_number: string;
  declare tax_number: string;
  declare tax_code: string;
  declare tax_rate: number;
  declare employment_status: string;
  declare is_invited: boolean;
  // declare hash: string;
  declare pension?: IPensionAttributes;
  declare leaves?: ILeaveAttributes[];
  declare payment_histories?: IPaymentHistoryAttributes[];
  declare deductibles?: IDeductibleAttributes[];

  declare readonly created_at: Date;
  declare readonly updated_at: Date;

  static associate(models: Models) {
    Employee.hasMany(models.Leave, {
      foreignKey: 'employee_id',
      as: 'leaves',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Employee.hasMany(models.PaymentHistory, {
      foreignKey: 'employee_id',
      as: 'payment_histories',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Employee.hasMany(models.Deductible, {
      foreignKey: 'employee_id',
      as: 'deductibles',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Employee.hasOne(models.Pension, {
      foreignKey: 'employee_id',
      as: 'pension',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }
}

Employee.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      unique: true,
      primaryKey: true,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: { model: 'organizations', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    middle_name: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    last_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    gender: {
      type: DataTypes.ENUM(...GENDER_ARRAY),
      allowNull: false,
    },
    date_of_birth: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    home_address: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: '',
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    phone_number: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    emergency_number: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    national_id: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    employment_type: {
      type: DataTypes.ENUM(...EMPLOYMENT_TYPE_ARRAY),
      allowNull: false,
    },
    employee_id: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    employment_start_date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    employment_end_date: {
      type: DataTypes.DATE,
      defaultValue: null,
      allowNull: true,
    },
    role: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    kind_of_payment: {
      type: DataTypes.ENUM(...KIND_OF_PAYMENT_ARRAY),
      allowNull: false,
    },
    mode_of_payment: {
      type: DataTypes.ENUM(...MODE_OF_PAYMENT_ARRAY),
      allowNull: false,
    },
    salary: {
      type: DataTypes.DECIMAL(18, 2),
      defaultValue: null,
      allowNull: true,
    },
    hourly_rate: {
      type: DataTypes.INTEGER,
      defaultValue: null,
      allowNull: true,
    },
    work_hours_per_week: {
      type: DataTypes.INTEGER,
      defaultValue: null,
      allowNull: true,
    },
    bonus_percent: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false,
    },
    bonus_interval: {
      type: DataTypes.ENUM(...BONUS_INTERVAL_ARRAY),
      allowNull: false,
      defaultValue: '',
    },
    bank_name: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    bank_account_name: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    bank_account_number: {
      type: DataTypes.STRING,
      defaultValue: '',
    },
    employment_status: {
      type: DataTypes.ENUM(...EMPLOYMENT_STATUS_ARRAY),
      defaultValue: EMPLOYMENT_STATUS_ENUM.ACTIVE,
      allowNull: false,
    },
    tax_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    tax_rate: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    tax_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_invited: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    sequelize,
    timestamps: true,
    underscored: true,
    indexes: [
      {
        name: 'employees_idx',
        unique: true,
        fields: [
          'email',
          'first_name',
          'last_name',
          'organization_id',
          'gender',
          'employment_type',
          'role',
          'is_invited',
        ],
      },
    ],
    modelName: 'Employee',
    tableName: 'employees',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default Employee;
