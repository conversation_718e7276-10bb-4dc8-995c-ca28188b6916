version: "3.9"

services:
  admin-app:
    image: "digit-tally-backend-admin-app:v1"
    secrets:
      - source: admin-app-env
        target: /app/.env
        uid: '103'
        gid: '103'
        mode: 0440
    networks:
      - dgt-network-v2-3:
      - shared-dgt-network-v2-3  
    deploy: &deploy
      mode: replicated
      replicas: 1
      rollback_config:
        parallelism: 2
        delay: 10s
        order: start-first
        failure_action: pause
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
        failure_action: rollback

networks:
  dgt-network-v2-3:
    external: true
  shared-dgt-network-v2-3:
    external: true 

secrets:
  admin-app-env:
    external: true
