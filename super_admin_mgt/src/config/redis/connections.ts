// import { createClient, RedisClientType } from 'redis';
// import logger from '../../utilities/logger';
// import { isTestENV } from '../../utilities/guards';

// const redisClient: RedisClientType = isTestENV
//   ? createClient()
//   : createClient({ url: process.env.REDIS_URL || 'redis://redis:6379' });

// redisClient.on('error', (error) => {
//   console.log('there is a redis error: ' + error);
//   logger.error(error);
//   process.exit(1);
// });

// export const connectRedis = async () => {
//   try {
//     await redisClient.connect();
//     return redisClient.isReady;
//   } catch (err) {
//     logger.error({
//       name: err.name,
//       location: 'Redis connected.',
//       message: err.message,
//       ...err,
//     });
//     process.exit(1);
//   }
// };

// export default redisClient;
