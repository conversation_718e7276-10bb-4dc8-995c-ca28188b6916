import Container from './container.global';
import UtilityController from '../controllers/utilities.controller';
import EmployeeControllers from '../controllers/employee.controller';
import services from './services.container';
import OrganizationControllers from '../controllers/organization.controller';
import SubscriptionPlanControllers from '../controllers/subscription-plan.controller';
import UserSubscriptionControllers from '../controllers/user-subscription.controller';
import DocumentControllers from '../controllers/documents.controller';
import UserControllers from '../controllers/users.controller';

const controllers = new Container('controllers');

controllers.register('utilityControllers', new UtilityController());

controllers.register(
  'employeeControllers',
  new EmployeeControllers(services.resolve('employeeService'))
);

controllers.register(
  'documentControllers',
  new DocumentControllers(services.resolve('documentServices'))
);

controllers.register(
  'organizationControllers',
  new OrganizationControllers(services.resolve('organizationServices'))
);

controllers.register(
  'SubscriptionPlanControllers',
  new SubscriptionPlanControllers(services.resolve('subscriptionPlanServices'))
);

controllers.register(
  'userSubscriptionControllers',
  new UserSubscriptionControllers(services.resolve('userSubscriptionServices'))
);

controllers.register('userControllers', new UserControllers(services.resolve('userServices')));

export default controllers;
