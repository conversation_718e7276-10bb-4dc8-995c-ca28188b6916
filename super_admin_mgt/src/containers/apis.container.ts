import DocumentAPIs from '../api/endpoints/document.apis';
import EmployeeAPIs from '../api/endpoints/employee.apis';
import OrganizationAPIs from '../api/endpoints/organization.apis';
import SubscriptionPlanAPIs from '../api/endpoints/subscription-plan.apis';
import UserSubscriptionAPIs from '../api/endpoints/user-subscriptions.apis';
import UserAPIs from '../api/endpoints/user.apis';
import UtilityAPIs from '../api/endpoints/utilities.api';
import Container from './container.global';

const apisContainer = new Container('apis');

apisContainer.register('employeeApis', new EmployeeAPIs());
apisContainer.register('documentApis', new DocumentAPIs());
apisContainer.register('subscriptionPlanApis', new SubscriptionPlanAPIs());
apisContainer.register('userSubscriptionApis', new UserSubscriptionAPIs());
apisContainer.register('organizationApis', new OrganizationAPIs());
apisContainer.register('userApis', new UserAPIs());
apisContainer.register('utilityApis', new UtilityAPIs());

export default apisContainer;
