import DocumentServices from '../services/document.service';
import EmployeeServices from '../services/employee.service';
import OrganizationServices from '../services/organization.service';
import SubscriptionPlanServices from '../services/subscription-plan.service';
import apisContainer from './apis.container';
import Container from './container.global';
import UserSubscriptionServices from '../services/user-subscription.service';
import UserServices from '../services/users.service';

//register all services here
const services = new Container('services');

services.register('employeeService', new EmployeeServices(apisContainer.resolve('employeeApis')));

services.register('documentServices', new DocumentServices(apisContainer.resolve('documentApis')));

services.register(
  'organizationServices',
  new OrganizationServices(apisContainer.resolve('organizationApis'))
);

services.register(
  'subscriptionPlanServices',
  new SubscriptionPlanServices(apisContainer.resolve('subscriptionPlanApis'))
);

services.register(
  'userSubscriptionServices',
  new UserSubscriptionServices(apisContainer.resolve('userSubscriptionApis'))
);

services.register('userServices', new UserServices(apisContainer.resolve('userApis')));

export default services;
