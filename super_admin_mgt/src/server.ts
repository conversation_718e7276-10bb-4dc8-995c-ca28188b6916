import dotenv from 'dotenv';
process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production'
  ? dotenv.config()
  : dotenv.config({ path: `${process.env.NODE_ENV}.env`, debug: true, encoding: 'utf8' });

import app from './app';
import logger from './utilities/logger';
import sequelize, { connectDb } from './config/database/connection';
import { Server } from 'http';

const serverName = 'Admin App Management MS. 💻';
const environment = `${process.env.NODE_ENV}`;
const port = process.env.PORT || 3001;

let server: Server;

async function startServer() {
  // connect and synchronize db.
  const dbName = await connectDb();

  // start server
  server = app.listen(port, () => {
    logger.info({
      serverName,
      environment,
      port,
      db: dbName ? `connected and synced to ${dbName} database` : `not connected to database`,
      startTimeStamp: new Date().toISOString(),
    });
  });
}

// close any connections here
const closeConnections = async () => {
  if (sequelize) await sequelize.close();
  return;
};

process.on('unhandledRejection', async (err: Error) => {
  await closeConnections();
  server.close(() => {
    logger.error({
      title: 'UNHANDLED REJECTION 💥 Shutting down...',
      name: err?.name,
      message: err?.message,
      serverName,
      stopTimeStamp: new Date().toISOString(),
      error: err,
    });
    process.exit(1);
  });
});

process.on('uncaughtException', async (err: Error) => {
  await closeConnections();
  server.close(() => {
    logger.error({
      title: 'UNCAUGHT EXCEPTION 💥 Shutting down...',
      name: err?.name,
      message: err?.message,
      serverName,
      stopTimeStamp: new Date().toISOString(),
      error: err,
    });
    process.exit(1);
  });
});

startServer();
