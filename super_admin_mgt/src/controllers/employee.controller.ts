import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import EmployeeServices from '../services/employee.service';
import { successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { getPagination } from '../utilities/global.utilities';

export default class EmployeeControllers extends RequestHandlerErrorWrapper {
  constructor(private employeeServices: EmployeeServices) {
    super();
  }

  async getAllOrganizationEmployees(req: Request, res: Response) {
    const { page, offset, limit } = getPagination(req);
    const { organizationId } = req.params;

    const employees = await this.employeeServices.getAllOrganizationEmployee(
      organizationId,
      offset,
      limit
    );

    const meta = { offset, limit, page, count: employees.length };

    return successResponse(
      res,
      RESPONSES.employeesRetrieved,
      'get all organization employees',
      employees,
      meta
    );
  }

  async getOneOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;

    const employee = await this.employeeServices.getOneOrganizationEmployee(
      organizationId,
      employeeId
    );

    return successResponse(
      res,
      RESPONSES.employeesRetrieved,
      'get one organization employee',
      employee
    );
  }

  async editOneOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const payload = req.body;

    const employee = await this.employeeServices.editOneOrganizationEmployee(
      organizationId,
      employeeId,
      payload
    );

    return successResponse(
      res,
      RESPONSES.employeeUpdated,
      'update organization employee',
      employee
    );
  }

  async changeOrganizationEmployeeStatus(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const employment_status = req.query.employment_status as string;

    const employee = await this.employeeServices.changeOrganizationEmployeeStatus(
      organizationId,
      employeeId,
      employment_status
    );

    return successResponse(
      res,
      RESPONSES.employeeStatusChanged,
      'change organization employee status',
      employee
    );
  }

  async createLeaveForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const payload = req.body;

    const leave = await this.employeeServices.createLeaveForOrganizationEmployee(
      organizationId,
      employeeId,
      payload
    );

    return successResponse(
      res,
      RESPONSES.leaveCreatedForEmployee,
      'create leave for organization employee',
      leave
    );
  }

  async getAllLeavesForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const { offset, limit, page } = getPagination(req);

    const leaves = await this.employeeServices.getAllLeavesForOrganizationEmployee(
      organizationId,
      employeeId,
      offset,
      limit
    );

    const meta = { page, offset, limit, count: leaves.length };

    return successResponse(
      res,
      RESPONSES.employeeLeavesRetrieved,
      'get all organization employee leaves',
      leaves,
      meta
    );
  }

  async getOneLeaveForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId, leaveId } = req.params;
    const leave = await this.employeeServices.getOneLeaveForOrganizationEmployee(
      organizationId,
      employeeId,
      leaveId
    );

    return successResponse(
      res,
      RESPONSES.employeeLeavesRetrieved,
      'get one organization employee leave',
      leave
    );
  }

  async getAllPaymentHistoryForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, employeeId } = req.params;
    const { offset, limit, page } = getPagination(req);

    const leaves = await this.employeeServices.getAllPaymentHistoryForOrganizationEmployee(
      organizationId,
      employeeId,
      offset,
      limit
    );

    const meta = { page, offset, limit, count: leaves.length };

    return successResponse(
      res,
      RESPONSES.employeePaymentHistoryRetrieved,
      'get all organization employee payment histories',
      leaves,
      meta
    );
  }

  async getOnePaymentHistoryForOrganizationEmployee(req: Request, res: Response) {
    const { organizationId, paymentHistoryId } = req.params;
    const leave = await this.employeeServices.getOnePaymentHistoryForOrganizationEmployee(
      organizationId,
      paymentHistoryId
    );

    return successResponse(
      res,
      RESPONSES.employeePaymentHistoryRetrieved,
      'get one organization employee payment history',
      leave
    );
  }
}
