import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import SubscriptionPlanServices from '../services/subscription-plan.service';
import { successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { getPagination } from '../utilities/global.utilities';

export default class SubscriptionPlanControllers extends RequestHandlerErrorWrapper {
  constructor(private subscriptionPlanServices: SubscriptionPlanServices) {
    super();
  }

  async getAllSubscriptionPlans(req: Request, res: Response) {
    const { offset, limit, page } = getPagination(req);

    const subscriptionPlans = await this.subscriptionPlanServices.getAllSubscriptionPlans(
      offset,
      limit
    );

    const meta = {
      offset,
      limit,
      page,
      count: subscriptionPlans.length,
    };

    return successResponse(
      res,
      RESPONSES.subscriptionPlansRetrieved,
      'get all subscription plans',
      subscriptionPlans,
      meta
    );
  }

  async getOneSubscriptionPlan(req: Request, res: Response) {
    const { planId } = req.params;
    const subscriptionPlan = await this.subscriptionPlanServices.getOneSubscriptionPlan(planId);

    return successResponse(
      res,
      RESPONSES.subscriptionPlansRetrieved,
      'get one subscription plan',
      subscriptionPlan
    );
  }

  async createSubscriptionPlan(req: Request, res: Response) {
    const payload = req.body;

    const subscriptionPlan = this.subscriptionPlanServices.createSubscriptionPlan(payload);

    return successResponse(
      res,
      RESPONSES.subscriptionPlanCreated,
      'create new subscription plan',
      subscriptionPlan
    );
  }

  async editSubscriptionPlan(req: Request, res: Response) {
    const { planId } = req.params;
    const payload = req.body;

    const subscriptionPlan = await this.subscriptionPlanServices.editSubscriptionPlan(
      planId,
      payload
    );

    return successResponse(
      res,
      RESPONSES.subscriptionPlanUpdated,
      'edit subscription plan',
      subscriptionPlan
    );
  }

  async editSubscriptionPlanStatus(req: Request, res: Response) {
    const { planId } = req.params;
    const { status } = req.query as { status: string };

    const subscriptionPlan = await this.subscriptionPlanServices.editSubscriptionPlanStatus(
      planId,
      status
    );

    return successResponse(
      res,
      RESPONSES.subscriptionPlanStatusUpdated,
      'edit subscription plan status',
      subscriptionPlan
    );
  }

  async deleteSubscriptionPlan(req: Request, res: Response) {
    const { planId } = req.params;

    await this.subscriptionPlanServices.deleteSubscriptionPlan(planId);

    return successResponse(res, RESPONSES.subscriptionPlanDeleted, 'delete subscription plan');
  }
}
