import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import UserSubscriptionServices from '../services/user-subscription.service';
import { successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';
import { getPagination } from '../utilities/global.utilities';

export default class UserSubscriptionControllers extends RequestHandlerErrorWrapper {
  constructor(private userSubscriptionServices: UserSubscriptionServices) {
    super();
  }

  async getUserSubscriptionStats(req: Request, res: Response) {
    const filter = req.query.filter as string;
    const stats = await this.userSubscriptionServices.getUserSubscriptionStats(filter);

    return successResponse(
      res,
      RESPONSES.userSubStatsRetrieved,
      'get user subscription stats',
      stats
    );
  }

  async getUserSubscriptionHistories(req: Request, res: Response) {
    const { startDate, endDate } = req.query as { startDate: string; endDate: string };
    const { offset, limit, page } = getPagination(req);

    const subscriptionHistories = await this.userSubscriptionServices.getUserSubscriptionHistories(
      startDate,
      endDate,
      offset,
      limit
    );

    const meta = { offset, limit, page, count: subscriptionHistories.length };

    return successResponse(
      res,
      RESPONSES.userSubHistoriesRetrieved,
      'get user subscription histories',
      subscriptionHistories,
      meta
    );
  }

  async getOneUserSubscriptionHistory(req: Request, res: Response) {
    const { subHistoryId } = req.params;

    const subscriptionHistories =
      await this.userSubscriptionServices.getOneUserSubscriptionHistory(subHistoryId);

    return successResponse(
      res,
      RESPONSES.userSubHistoriesRetrieved,
      'get one user subscription history',
      subscriptionHistories
    );
  }
}
