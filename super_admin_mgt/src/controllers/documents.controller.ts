import { Request, Response } from 'express';
import { RequestHandlerErrorWrapper } from '../helpers/class.helpers';
import DocumentServices from '../services/document.service';
import { getPagination } from '../utilities/global.utilities';
import { successResponse } from '../helpers/response.helpers';
import { RESPONSES } from '../constants/responses.constants';

export default class DocumentControllers extends RequestHandlerErrorWrapper {
  constructor(private documentServices: DocumentServices) {
    super();
  }

  async getAllOrganizationDocuments(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { offset, limit, page } = getPagination(req);

    const documents = await this.documentServices.getAllOrganizationDocuments(
      organizationId,
      offset,
      limit
    );

    const meta = { offset, limit, page, count: documents.length };
    return successResponse(
      res,
      RESPONSES.documentsRetrieved,
      'get all organization documents',
      documents,
      meta
    );
  }

  async getRecentOrganizationDocuments(req: Request, res: Response) {
    const { organizationId } = req.params;

    const documents = await this.documentServices.getRecentOrganizationDocuments(organizationId);
    return successResponse(
      res,
      RESPONSES.documentsRetrieved,
      'get recent organization documents',
      documents
    );
  }

  async searchOrganizationDocuments(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { offset, limit, page } = getPagination(req);
    const searchParams = req.query;

    const documents = await this.documentServices.searchOrganizationDocuments(
      organizationId,
      searchParams,
      offset,
      limit
    );

    const meta = { offset, limit, page, count: documents.length };
    return successResponse(
      res,
      RESPONSES.documentsRetrieved,
      'search organization documents',
      documents,
      meta
    );
  }

  async getOrganizationDocumentById(req: Request, res: Response) {
    const { organizationId, documentId } = req.params;

    const document = await this.documentServices.getOrganizationDocumentByDocumentId(
      organizationId,
      documentId
    );

    return successResponse(
      res,
      RESPONSES.documentsRetrieved,
      'get one organization document',
      document
    );
  }

  async createReceipt(req: Request, res: Response) {
    const { organizationId } = req.params;
    const payload = req.body;

    const receipt = await this.documentServices.createReceiptForOrganization(
      organizationId,
      payload
    );
    return successResponse(
      res,
      RESPONSES.receiptCreated,
      'create receipt for organization',
      receipt
    );
  }

  async createInvoice(req: Request, res: Response) {
    const { organizationId } = req.params;
    const payload = req.body;

    const invoice = await this.documentServices.createInvoiceForOrganization(
      organizationId,
      payload
    );
    return successResponse(
      res,
      RESPONSES.invoiceCreated,
      'create invoice for organization',
      invoice
    );
  }

  async createCreditNote(req: Request, res: Response) {
    const { organizationId } = req.params;
    const payload = req.body;

    const creditNote = await this.documentServices.createCreditNoteForOrganization(
      organizationId,
      payload
    );
    return successResponse(
      res,
      RESPONSES.creditNoteCreated,
      'create credit note for organization',
      creditNote
    );
  }

  async archiveDocument(req: Request, res: Response) {
    const { organizationId, documentId } = req.params;

    const result = await this.documentServices.archiveOrganizationDocument(
      organizationId,
      documentId
    );
    return successResponse(res, RESPONSES.documentArchived, 'archive document', result);
  }

  async unarchiveDocument(req: Request, res: Response) {
    const { organizationId, documentId } = req.params;

    const result = await this.documentServices.unarchiveOrganizationDocument(
      organizationId,
      documentId
    );
    return successResponse(res, RESPONSES.documentUnArchived, 'unarchive document', result);
  }

  async getAllArchivedDocuments(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { offset, limit, page } = getPagination(req);

    const documents = await this.documentServices.getAllArchivedOrganizationDocuments(
      organizationId,
      offset,
      limit
    );

    const meta = { offset, limit, page, count: documents.length };
    return successResponse(
      res,
      RESPONSES.documentsRetrieved,
      'get all archived documents',
      documents,
      meta
    );
  }

  async getArchivedDocumentById(req: Request, res: Response) {
    const { organizationId, documentId } = req.params;

    const document = await this.documentServices.getArchivedOrganizationDocumentById(
      organizationId,
      documentId
    );

    return successResponse(
      res,
      RESPONSES.documentsRetrieved,
      'get archived document by id',
      document
    );
  }

  async downloadDocument(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { documentNumber } = req.query;

    const file = await this.documentServices.downloadOrganizationDocument(
      organizationId,
      String(documentNumber)
    );

    res.setHeader('Content-Disposition', `attachment; filename="${documentNumber}.pdf"`);
    res.setHeader('Content-Type', 'application/pdf');
    return res.send(file);
  }

  async sendDocument(req: Request, res: Response) {
    const { organizationId } = req.params;
    const payload = req.body;

    const result = await this.documentServices.sendOrganizationDocument(organizationId, payload);
    return successResponse(res, RESPONSES.documentSent, 'send organization document', result);
  }

  async sendDocumentReminder(req: Request, res: Response) {
    const { organizationId } = req.params;
    const payload = req.body;

    const result = await this.documentServices.sendDocumentReminder(organizationId, payload);
    return successResponse(res, RESPONSES.documentReminderSent, 'send document reminder', result);
  }

  async createCustomer(req: Request, res: Response) {
    const { organizationId } = req.params;
    const payload = req.body;

    const customer = await this.documentServices.createCustomerForOrganization(
      organizationId,
      payload
    );
    return successResponse(
      res,
      RESPONSES.customerCreated,
      'create customer for organization',
      customer
    );
  }

  async getAllCustomers(req: Request, res: Response) {
    const { organizationId } = req.params;
    const { offset, limit, page } = getPagination(req);

    const customers = await this.documentServices.getAllCustomersForOrganization(
      organizationId,
      offset,
      limit
    );

    const meta = { offset, limit, page, count: customers.length };
    return successResponse(res, RESPONSES.customersRetrieved, 'get all customers', customers, meta);
  }

  async getCustomerById(req: Request, res: Response) {
    const { organizationId, customerId } = req.params;

    const customer = await this.documentServices.getOneCustomerForOrganization(
      organizationId,
      customerId
    );

    return successResponse(res, RESPONSES.customersRetrieved, 'get customer by id', customer);
  }

  async updateCustomer(req: Request, res: Response) {
    const { organizationId, customerId } = req.params;
    const payload = req.body;

    const customer = await this.documentServices.updateCustomerForOrganization(
      organizationId,
      customerId,
      payload
    );

    return successResponse(res, RESPONSES.customerUpdated, 'update customer', customer);
  }
}
