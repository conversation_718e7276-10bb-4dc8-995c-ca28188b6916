import { Router } from 'express';
import controllers from '../containers/controllers.container';
import { validateRouteIdParameter } from '../middlewares/validators/global.validators';

const plansManagementRouter = Router();
const plansController = controllers.resolve('SubscriptionPlanControllers');
const planId = 'planId';

plansManagementRouter
  .route('/')
  .get(plansController.getAllSubscriptionPlans)
  .post(plansController.createSubscriptionPlan);

plansManagementRouter
  .route(`/:${planId}`)
  .all(validateRouteIdParameter(planId))
  .get(plansController.getOneSubscriptionPlan)
  .patch(plansController.editSubscriptionPlanStatus)
  .put(plansController.editSubscriptionPlan)
  .delete(plansController.deleteSubscriptionPlan);

export default plansManagementRouter;
