import { Router } from 'express';
import controllers from '../containers/controllers.container';
import {
  validateQueryParameter,
  validateRouteIdParameter,
} from '../middlewares/validators/global.validators';
import { dayWeekMonthYearSchema } from '../middlewares/validators/schemas/query-params.schema';

const userManagementRouter = Router();
const userControllers = controllers.resolve('userControllers');
const userId = 'userId';

userManagementRouter.get('/', userControllers.getAllUsers);
userManagementRouter.get(
  '/stats',
  validateQueryParameter(dayWeekMonthYearSchema),
  userControllers.getUserStats
);

userManagementRouter
  .route(`/:${userId}`)
  .all(validateRouteIdParameter(userId))
  .get(userControllers.getOneUser);

export default userManagementRouter;
