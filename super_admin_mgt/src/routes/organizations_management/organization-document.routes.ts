import { Router } from 'express';
import controllers from '../../containers/controllers.container';
import { validateRouteIdParameter } from '../../middlewares/validators/global.validators';

const organizationDocumentRouter = Router({ mergeParams: true });
const documentControllers = controllers.resolve('documentControllers');

// document ID and Customer ID params
const documentId = 'documentId';
const customerId = 'customerId';

organizationDocumentRouter.get('/', documentControllers.getAllOrganizationDocuments);

organizationDocumentRouter.get('/recent', documentControllers.getRecentOrganizationDocuments);

organizationDocumentRouter.get('/search', documentControllers.searchOrganizationDocuments);

organizationDocumentRouter
  .route(`/:${documentId}`)
  .all(validateRouteIdParameter(documentId))
  .get(documentControllers.getOrganizationDocumentById);

// download
organizationDocumentRouter.get('/download', documentControllers.downloadDocument);

// // send and reminder
// organizationDocumentRouter.post('/send', documentControllers.sendDocument);
// organizationDocumentRouter.post('/send-reminder', documentControllers.sendDocumentReminder);

// archive / unarchive
// organizationDocumentRouter
//   .route(`/:${documentId}/archive`)
//   .all(validateRouteIdParameter(documentId))
//   .patch(documentControllers.archiveDocument);

// organizationDocumentRouter
//   .route(`/:${documentId}/unarchive`)
//   .all(validateRouteIdParameter(documentId))
//   .patch(documentControllers.unarchiveDocument);

// archived docs
organizationDocumentRouter.get('/archived', documentControllers.getAllArchivedDocuments);

organizationDocumentRouter
  .route(`/archived/:${documentId}`)
  .all(validateRouteIdParameter(documentId))
  .get(documentControllers.getArchivedDocumentById);

// create documents
// organizationDocumentRouter.post('/receipts', documentControllers.createReceipt);
// organizationDocumentRouter.post('/invoices', documentControllers.createInvoice);
// organizationDocumentRouter.post('/credit-notes', documentControllers.createCreditNote);

// customers
organizationDocumentRouter
  .route('/customers')
  // .post(documentControllers.createCustomer)
  .get(documentControllers.getAllCustomers);

organizationDocumentRouter
  .route(`/customers/:${customerId}`)
  .all(validateRouteIdParameter(customerId))
  .get(documentControllers.getCustomerById);
// .put(documentControllers.updateCustomer);

export default organizationDocumentRouter;
