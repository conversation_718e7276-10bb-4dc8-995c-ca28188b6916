import { Router } from 'express';
import utilityRouter from './utilities.routes';
import controllers from '../containers/controllers.container';
import middlewares from '../containers/middlewares.container';
import { API_VERSION } from '../constants/values.constants';
import organizationManagementRouter from './organizations_management/index.routes';
import plansManagementRouter from './plans-management.routes';
import subscriptionsManagementRouter from './subscription-management.routes';
import userManagementRouter from './users.routes';

const globalRouter = Router();
const auth = middlewares.resolve('authMiddlewares');
const { captureAppDetails } = middlewares.resolve('utilityMiddlewares');

globalRouter.use(captureAppDetails);

//use all app routers here to handle specific urls
globalRouter.use(`${API_VERSION}`, utilityRouter);

globalRouter.use(auth.authenticate.bind(auth));
// globalRouter.use(`${API_VERSION}/users`);
globalRouter.use(`${API_VERSION}/organizations`, organizationManagementRouter);
globalRouter.use(`${API_VERSION}/subscriptions`, subscriptionsManagementRouter);
globalRouter.use(`${API_VERSION}/plans`, plansManagementRouter);
globalRouter.use(`${API_VERSION}/users`, userManagementRouter);

globalRouter.all('*', controllers.resolve('utilityControllers').resourceNotFound);

export default globalRouter;
