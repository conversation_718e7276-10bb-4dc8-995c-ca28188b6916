import { isDevelopmentEnv, isTestENV } from '../utilities/guards';

export const API_VERSION = `/api/v1/admin-mgt`;

export const HUNDRED = 100;
export const BASE_10 = 10;
export const ZERO = 0;
export const OTP_EXPIRE_TIME: number = 15;

export const FILE_EXTENSION = {
  PDF: 'pdf',
  JPEG: 'jpeg',
  JPG: 'jpg',
  PNG: 'png',
};

const BASE_ORIGINS = ['https://digit-tally.io'];

export const HTTP_METHODS = {
  POST: 'POST',
  GET: 'GET',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const DEV_ORIGINS = [
  'http://localhost:9628',
  'http://127.0.0.1:9628',
  'http://localhost:8752',
  'http://127.0.0.1:8752',
  'http://localhost:8756',
  'http://127.0.0.1:8756',
  'http://localhost:8759',
  'http://127.0.0.1:8759',
  'http://localhost:3932',
  'http://127.0.0.1:3932',
  'http://localhost:4512',
  'http://127.0.0.1:4512',
  'https://localhost:9628',
  'https://127.0.0.1:9628',
  'https://localhost:8752',
  'https://127.0.0.1:8752',
  'https://localhost:3932',
  'https://127.0.0.1:3932',
  'https://localhost:4512',
  'https://127.0.0.1:4512',
  'http://***********:8080',
  'http://*************:3000',
  'http://*************:3000',
  'http://*************:3000',
  'https://stgsvr008.digit-tally.io',
];

export const APP_ORIGINS = {
  DEV_USER_APP: process.env.DEV_USER_APP,
  DEV_ADMIN: process.env.DEV_ADMIN_APP,
  PROD_USER_APP: process.env.PROD_USER_APP,
  PROD_ADMIN: process.env.PROD_ADMIN_APP,
};

export const ALLOWED_ORIGINS =
  isDevelopmentEnv || isTestENV
    ? [...DEV_ORIGINS, ...BASE_ORIGINS, process.env.DEV_USER_APP, process.env.DEV_ADMIN_APP]
    : [...BASE_ORIGINS, process.env.PROD_USER_APP, process.env.PROD_ADMIN_APP];

export const ADMIN_ORIGINS = [process.env.PROD_ADMIN_APP, process.env.DEV_ADMIN_APP];

export const USER_APP_ORIGINS =
  isDevelopmentEnv || isTestENV
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP]
    : [APP_ORIGINS.DEV_USER_APP, APP_ORIGINS.PROD_USER_APP];

export const ADMIN_APP_ORIGINS =
  isDevelopmentEnv || isTestENV
    ? [...DEV_ORIGINS, APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN]
    : [APP_ORIGINS.DEV_ADMIN, APP_ORIGINS.PROD_ADMIN];

export const DEFINED_MS_ERROR_CODES_WITH_MESSAGES = {
  400: 'EB400',
  401: 'EA401',
  403: 'EP403',
  404: 'EN404',
  409: 'EC409',
  500: 'ES500',
} as const;

export const DEFINED_MS_ERROR_CODES_ARRAY = Object.values(DEFINED_MS_ERROR_CODES_WITH_MESSAGES);

export const ADMIN_ROLES = ['superadmin'] as const;

export const DAY_WEEK_MONTH_YEAR_FILTER = {
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly',
  yearly: 'yearly',
};

export const DAY_WEEK_MONTH_YEAR_FILTER_ARRAY = Object.values(DAY_WEEK_MONTH_YEAR_FILTER);
