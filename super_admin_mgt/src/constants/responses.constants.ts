import { StatusCodes } from 'http-status-codes';

function defineResponses<T extends Record<string, readonly [string, number]>>(responses: T) {
  return responses;
}
// Define your custom responses here
export const RESPONSES = defineResponses({
  serverIsActive: ['admin app is running successfully', StatusCodes.OK],
  employeesRetrieved: ['organization employee(s) retrieved successfully', StatusCodes.OK],
  employeeUpdated: ['organization employee updated successfully', StatusCodes.CREATED],
  employeeStatusChanged: ['organization employee status changed successfully', StatusCodes.CREATED],
  leaveCreatedForEmployee: ['leave created for employee successfully', StatusCodes.CREATED],
  employeeLeavesRetrieved: [
    'organization employee leave(s) retrieved successfully',
    StatusCodes.OK,
  ],
  employeePaymentHistoryRetrieved: [
    'organization employee payment history(ies) retrieved successfully',
    StatusCodes.OK,
  ],
  documentsRetrieved: ['organization document(s) retrieved successfully', StatusCodes.OK],
  customersRetrieved: ['organization customers(s) retrieved successfully', StatusCodes.OK],
  customerCreated: ['customer created for organization successfully', StatusCodes.CREATED],
  customerUpdated: ['customer updated successfully', StatusCodes.CREATED],
  invoiceCreated: ['invoice created for organization successfully', StatusCodes.CREATED],
  receiptCreated: ['receipt created for organization successfully', StatusCodes.CREATED],
  creditNoteCreated: ['credit note created for organization successfully', StatusCodes.CREATED],
  documentArchived: ['document archived successfully', StatusCodes.CREATED],
  documentUnArchived: ['document un-archived successfully', StatusCodes.CREATED],
  documentSent: ['document sent successfully', StatusCodes.OK],
  documentReminderSent: ['document reminder sent successfully', StatusCodes.OK],
  subscriptionPlansRetrieved: ['subscription plan(s) retrieved successfully', StatusCodes.OK],
  subscriptionPlanCreated: ['subscription plan created successfully', StatusCodes.CREATED],
  subscriptionPlanUpdated: ['subscriptionPlan updated successfully', StatusCodes.CREATED],
  subscriptionPlanStatusUpdated: [
    'subscriptionPlan status updated successfully',
    StatusCodes.CREATED,
  ],
  subscriptionPlanDeleted: ['subscriptionPlan status deleted successfully', StatusCodes.NO_CONTENT],
  userSubStatsRetrieved: ['user subscriptions stats retrieved successfully', StatusCodes.OK],
  userSubHistoriesRetrieved: [
    'user subscription history(ies) retrieved successfully',
    StatusCodes.OK,
  ],
  organizationsRetrieved: ['organization(s) retrieved successfully', StatusCodes.OK],
  usersRetrieved: ['user(s) retrieved successfully', StatusCodes.OK],
  userStatsRetrieved: ['user(s) stats retrieved successfully', StatusCodes.OK],
});
