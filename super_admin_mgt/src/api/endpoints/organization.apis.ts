import axiosInstance from '../../config/axios';
import { HTTP_METHODS } from '../../constants/values.constants';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { ADMIN_BASE_URL } from '../urls';
import { getRequestOptions } from '../helpers';

export default class OrganizationAPIs extends <PERSON><PERSON>r<PERSON>rapper {
  // get all organizations
  async getAllOrganizations(offset = 1, limit = 50): Promise<any[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/account/organizations`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // get one organization
  async getOneOrganization(organizationId: string): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/account/organizations/${organizationId}`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }
}
