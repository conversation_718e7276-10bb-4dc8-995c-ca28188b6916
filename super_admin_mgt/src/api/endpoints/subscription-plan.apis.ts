import axiosInstance from '../../config/axios';
import { HTTP_METHODS } from '../../constants/values.constants';
import { ErrorWrapper } from '../../helpers/class.helpers';
import { ADMIN_BASE_URL } from '../urls';
import { getRequestOptions } from '../helpers';

export default class SubscriptionPlanAPIs extends <PERSON><PERSON>r<PERSON>rapper {
  async getAllSubscriptionPlans(offset = 1, limit = 50): Promise<Record<string, any>[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/subscription-plans`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getOneSubscriptionPlan(planId: string): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/subscription-plans/${planId}`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async createSubscriptionPlan(payload: any): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/subscription-plans`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      payload,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async editSubscriptionPlan(planId: string, payload: any): Promise<Record<string, any>> {
    const method = HTTP_METHODS.PUT;
    const url = `${ADMIN_BASE_URL}/subscription-plans/${planId}`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      payload,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async editSubscriptionPlanStatus(planId: string, status: string): Promise<Record<string, any>> {
    const method = HTTP_METHODS.PATCH;
    const url = `${ADMIN_BASE_URL}/subscription-plans/${planId}`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { status },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async deleteSubscriptionPlan(planId: string) {
    const method = HTTP_METHODS.DELETE;
    const url = `${ADMIN_BASE_URL}/subscription-plans/${planId}`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }
}
