import axiosInstance from '../../config/axios';
import { HTTP_METHODS } from '../../constants/values.constants';
import { <PERSON>rrorWrapper } from '../../helpers/class.helpers';
import { ISendDocumentRequestBody } from '../../interfaces/document.interfaces';
import { getRequestOptions } from '../helpers';
import { ADMIN_BASE_URL } from '../urls';

export default class DocumentAPIs extends E<PERSON>r<PERSON>rapper {
  // get all organization documents
  async getAllOrganizationDocuments(
    organizationId: string,
    offset = 1,
    limit = 50
  ): Promise<any[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // get recent documents
  async getRecentOrganizationDocuments(organizationId: string): Promise<any[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/recent`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // search organization documents
  async searchOrganizationDocuments(
    organizationId: string,
    searchParams: Record<string, any>,
    offset = 1,
    limit = 50
  ): Promise<any[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/search`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { ...searchParams, offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // get one organization document
  async getOrganizationDocumentByDocumentId(
    organizationId: string,
    documentId: string
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/${documentId}`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // create receipt for organization
  async createReceiptForOrganization(
    organizationId: string,
    payload: any
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/receipt`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true, payload });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // create invoice for organization
  async createInvoiceForOrganization(
    organizationId: string,
    payload: any
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/invoice`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true, payload });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // create credit note for organization
  async createCreditNoteForOrganization(
    organizationId: string,
    payload: any
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/creditnote`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true, payload });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // archive a document
  async archiveOrganizationDocument(
    organizationId: string,
    documentId: string
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.PATCH;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/${documentId}/archive`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // unarchive a document
  async unarchiveOrganizationDocument(
    organizationId: string,
    documentId: string
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.PATCH;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/${documentId}/unarchive`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // get all archived documents
  async getAllArchivedOrganizationDocuments(
    organizationId: string,
    offset = 1,
    limit = 50
  ): Promise<any[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/archives`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // get one archived document
  async getArchivedOrganizationDocumentById(
    organizationId: string,
    documentId: string
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/archived/${documentId}`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // download a document
  async downloadOrganizationDocument(organizationId: string, documentNumber: string) {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/download`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { documentNumber },
      responseType: 'blob',
    });

    const response = await axiosInstance.request(options);
    return response.data;
  }

  // send a document
  async sendOrganizationDocument(
    organizationId: string,
    payload: { email: string; documentNumber: string }
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/send`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      payload,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // send a document reminder
  async sendDocumentReminder(
    organizationId: string,
    payload: ISendDocumentRequestBody
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/send-reminder`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      payload,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // create customer for organization
  async createCustomerForOrganization(
    organizationId: string,
    payload: any
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/customers`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true, payload });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getAllCustomersForOrganization(
    organizationId: string,
    offset = 1,
    limit = 50
  ): Promise<any[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/customers`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getOneCustomerForOrganization(
    organizationId: string,
    customerId: string
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/customers/${customerId}`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  // update customer for organization
  async updateCustomerForOrganization(
    organizationId: string,
    customerId: string,
    payload: any
  ): Promise<Record<string, any>> {
    const method = HTTP_METHODS.PUT;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/documents/customers/${customerId}`;

    const options = getRequestOptions({ method, url, authHeader: true, reqId: true, payload });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }
}
