import { ErrorWrapper } from '../../helpers/class.helpers';
import { HTTP_METHODS } from '../../constants/values.constants';
import { getRequestOptions } from '../helpers';
import { ADMIN_BASE_URL } from '../urls';
import axiosInstance from '../../config/axios';
import {
  IEditEmployeeAttributes,
  IEmployeeAttributes,
  IGetAllEmployeeResponse,
  ILeaveAttributes,
  IPaymentHistoryAttributes,
} from '../../interfaces/employee.interfaces';

export default class EmployeeAPIs extends ErrorWrapper {
  async getAllOrganizationEmployees(
    organizationId: string,
    offset = 1,
    limit = 50
  ): Promise<IGetAllEmployeeResponse[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getOneOrganizationEmployee(
    organizationId: string,
    employeeId: string
  ): Promise<IEmployeeAttributes> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/${employeeId}`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async editOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    body: IEditEmployeeAttributes
  ): Promise<IEmployeeAttributes> {
    const method = HTTP_METHODS.PUT;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/${employeeId}`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      payload: body,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async changeOrganizationEmployeeStatus(
    organizationId: string,
    employeeId: string,
    employmentStatus: string
  ): Promise<IEmployeeAttributes> {
    const method = HTTP_METHODS.PATCH;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/${employeeId}/status`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { employment_status: employmentStatus },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async createLeaveForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    body: ILeaveAttributes
  ): Promise<ILeaveAttributes> {
    const method = HTTP_METHODS.POST;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/leaves`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { employeeId },
      payload: body,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getAllLeavesForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    offset = 1,
    limit = 50
  ): Promise<ILeaveAttributes[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/leaves`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { employeeId, offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getOneLeaveForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    leaveId: string
  ): Promise<ILeaveAttributes> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/${employeeId}/leaves/${leaveId}`;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getAllPaymentHistoryForOrganizationEmployee(
    organizationId: string,
    employeeId: string,
    offset = 1,
    limit = 50
  ): Promise<IPaymentHistoryAttributes[]> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/employee-payments`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
      params: { employeeId, offset, limit },
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }

  async getOnePaymentHistoryForOrganizationEmployee(
    organizationId: string,
    paymentHistoryId: string
  ): Promise<IPaymentHistoryAttributes> {
    const method = HTTP_METHODS.GET;
    const url = `${ADMIN_BASE_URL}/organizations/${organizationId}/employees/employee-payments/${paymentHistoryId}`;
    // const accessKey = process.env.USERS_SERVICE_KEY;
    // const apiKey = process.env.GATEWAY_API_KEY;

    const options = getRequestOptions({
      method,
      url,
      authHeader: true,
      reqId: true,
    });

    const response = await axiosInstance.request(options);
    return response.data.data;
  }
}
