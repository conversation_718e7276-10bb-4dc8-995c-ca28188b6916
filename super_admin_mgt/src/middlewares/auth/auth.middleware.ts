import { RequestHandlerErrorWrapper } from '../../helpers/class.helpers';
import { NotAuthenticatedError, NotPermittedError } from '../../helpers/error.helpers';
import { Request, Response, NextFunction } from 'express';
import httpContext from 'express-http-context';
import { IAdminUser } from '../../interfaces/user.interface';
import UserAPIs from '../../api/endpoints/user.apis';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user: IAdminUser;
    }
  }
}

// type AdminRole = (typeof ADMIN_ROLES)[number];

// function isAdminRole(role: string): role is AdminRole {
//   return ADMIN_ROLES.includes(role as AdminRole);
// }

export default class AuthMiddlewares extends RequestHandlerErrorWrapper {
  constructor(private userApis: UserAPIs) {
    super();
  }
  async authenticate(req: Request, res: Response, next: NextFunction) {
    const authHeader = req.headers['authorization'];
    if (!authHeader) throw new NotAuthenticatedError();

    httpContext.set('authHeader', authHeader);

    const user = await this.userApis.getMyAccount();
    if (!user) return;

    if (user && user?.isSuperAdmin && user?.globalAccess) {
      req.user = user;
      return next();
    }

    throw new NotPermittedError();
  }

  // @exemptFromErrorWrapping
  // verifyPermission(allowedRoles: string[]) {
  //   return catchAsync(async (req: Request, _res: Response, next: NextFunction) => {
  //     allowedRoles = allowedRoles.map((allowedRole) => allowedRole.toLowerCase());
  //     const userRole = String(req.user.role).toLowerCase();

  //     if (!allowedRoles.includes(userRole)) {
  //       throw new NotPermittedError();
  //     }

  //     next();
  //   });
  // }
}
