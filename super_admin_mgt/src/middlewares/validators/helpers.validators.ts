import { CustomHelpers, ValidationError } from 'joi';
import {
  IDeductibleAttributes,
  IEditEmployeeAttributes,
  IEmployeeAttributes,
  ILeaveAttributes,
} from '../../interfaces/employee.interfaces';
import {
  EMPLOYMENT_TYPE_ENUM,
  KIND_OF_PAYMENT_ENUM,
  MODE_OF_PAYMENT_ENUM,
} from '../../models/enums';

export function getJoiValidationErrorMessage(error: ValidationError) {
  const errorMessage = error.details
    .map((detail) => {
      return detail.message.replace(/"+/g, '');
    })
    .join(', ');

  return errorMessage;
}

export const createEmployeeCustomValidator = (
  value: IEmployeeAttributes,
  helpers: CustomHelpers
) => {
  //  If employment type is contract, employment_end_date must be present
  if (value.employment_type === EMPLOYMENT_TYPE_ENUM.CONTRACT && !value.employment_end_date) {
    return helpers.error('date.employmentEndDateIsRequired');
  }

  //  If mode of payment is electronic, bank details must be present
  if (value.mode_of_payment === MODE_OF_PAYMENT_ENUM.ELECTRONIC) {
    if (!value.bank_name || !value.bank_account_name || !value.bank_account_number) {
      return helpers.error('string.bankDetailsAreRequired');
    }
  }

  //  If any bank details are present, all three must be present
  const bankDetails = [value.bank_name, value.bank_account_name, value.bank_account_number];
  const bankDetailsPresent = bankDetails.filter((detail) => detail && detail.trim() !== '').length;
  if (bankDetailsPresent > 0 && bankDetailsPresent < 3) {
    return helpers.error('string.bankDetailsMustBeComplete');
  }

  // If kind of payment is hourly, hourly_rate and work_hours_per_week must be present
  if (value.kind_of_payment === KIND_OF_PAYMENT_ENUM.HOURLY) {
    if (!value.hourly_rate || !value.work_hours_per_week) {
      return helpers.error('string.hourlyRateAndWorkHoursAreRequired');
    }
  }

  if (value.salary && value.hourly_rate) {
    return helpers.error('string.eitherSalaryOrHourlyRateMustBeProvided');
  }

  if (value.kind_of_payment === KIND_OF_PAYMENT_ENUM.SALARY && !value.salary) {
    return helpers.error('string.salaryMustBeProvided');
  }

  return value;
};

export const createDeductibleCustomValidator = (
  value: IDeductibleAttributes,
  helpers: CustomHelpers
) => {
  let { one_time, start_date, end_date } = value;

  one_time = one_time || false;
  start_date = new Date(start_date);
  end_date = new Date(end_date);

  if (one_time && start_date !== end_date) {
    return helpers.error('date.oneTime');
  }

  if (!one_time && end_date <= start_date) {
    return helpers.error('date.notOneTime');
  }

  if (end_date < start_date) {
    return helpers.error('date.endDateMustBeGreaterOrEqualToStartDate');
  }

  return value;
};

export const editEmployeeCustomValidator = (
  value: IEditEmployeeAttributes,
  helpers: CustomHelpers
) => {
  if (
    !value.employee_details &&
    !value.pension &&
    (!value.deductibles || value.deductibles?.length === 0)
  ) {
    return helpers.error('any.required');
  }
  return value;
};

export const createLeaveCustomValidator = (value: ILeaveAttributes, helpers: CustomHelpers) => {
  //end date must be greater than or equal to start date
  if (
    value.end_date &&
    value.start_date &&
    new Date(value.end_date).setHours(0, 0, 0, 0) < new Date(value.start_date).setHours(0, 0, 0, 0)
  ) {
    return helpers.error('date.endDateMustBeGreaterThanStartDate');
  }
  return value;
};
