import Joi from 'joi';

const editCustomerContactSchema: Jo<PERSON>.ObjectSchema = Joi.object({
  email: Joi.string().email({ minDomainSegments: 2 }).trim().lowercase().allow('').allow(''),
  firstname: Joi.string().allow(''),
  lastname: Joi.string().allow(''),
  phoneNumber: Joi.string().allow(''),
  id: Joi.string()
    .guid({ version: ['uuidv4'] })
    .optional()
    .messages({
      'string.guid': 'customer contact ID must be a valid UUID',
    }),
});

const customerContactSchema = Joi.object({
  email: Joi.string().email({ minDomainSegments: 2 }).required().messages({
    'any.required': 'customer contact email is required.',
  }),
  firstname: Joi.string().required().messages({
    'any.required': 'Please enter your contact first name',
  }),
  lastname: Joi.string().required().messages({
    'any.required': 'Please enter your contact last name',
  }),
  phoneNumber: Joi.string().required().messages({
    'any.required': 'Please enter your contact Phone number',
  }),
});

const customerSchema: Joi.ObjectSchema = Joi.object({
  email: Joi.string().email({ minDomainSegments: 2 }).allow('').messages({
    'string.base': 'Your email must be a string',
    'string.email': 'Please enter a valid email address',
  }),
  name: Joi.string().required().messages({
    'any.required': 'Customer name is required',
  }),
  contacts: Joi.array().items(customerContactSchema).optional().allow(''),
  website: Joi.string().allow(''),
  companyRegNum: Joi.string().allow(''),
  phoneNumber: Joi.string().allow('').messages({
    'string.base': 'Customer phone number must be a string',
  }),
  address: Joi.string().allow(''),
});

export const editCustomerSchema: Joi.ObjectSchema = Joi.object()
  .required()
  .keys({
    email: Joi.string().email({ minDomainSegments: 2 }).trim().lowercase().allow(''),
    name: Joi.string().required().messages({
      'any.required': 'Customer name is required',
    }),
    contacts: Joi.array().items(editCustomerContactSchema).allow('').optional(),
    website: Joi.string().allow(''),
    companyRegNum: Joi.string().allow(''),
    phoneNumber: Joi.string().required().messages({
      'any.required': 'Customer phone number is required',
    }),
    address: Joi.string().allow(''),
  });

export const createCustomerSchema = Joi.object()
  .required()
  .keys({
    customers: Joi.array()
      .items(customerSchema)
      .min(1)
      .required()
      .messages({ 'any.required': 'an array of customer details is required' }),
  });
