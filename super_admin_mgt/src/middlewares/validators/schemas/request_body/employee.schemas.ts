import Joi from 'joi';
import {
  IEditEmployeeAttributes,
  ILeaveAttributes,
} from '../../../../interfaces/employee.interfaces';
import {
  BONUS_INTERVAL_ARRAY,
  BONUS_INTERVAL_ENUM,
  EMPLOYMENT_STATUS_ARRAY,
  EMPLOYMENT_STATUS_ENUM,
  EMPLOYMENT_TYPE_ARRAY,
  GENDER_ARRAY,
  KIND_OF_PAYMENT_ARRAY,
  LEAVE_TYPE_ARRAY,
  MODE_OF_PAYMENT_ARRAY,
} from '../../../../models/enums';
import {
  createDeductibleCustomValidator,
  createEmployeeCustomValidator,
  createLeaveCustomValidator,
  editEmployeeCustomValidator,
} from '../../helpers.validators';

export const editEmployeeSchema = Joi.object<IEditEmployeeAttributes>({
  employee_details: Joi.object({
    first_name: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'First name must be a string.',
      'string.min': 'First name must be at least 2 characters long.',
      'string.max': 'First name must not exceed 50 characters.',
    }),

    middle_name: Joi.string().trim().lowercase().min(0).max(50).allow('').optional().messages({
      'string.base': 'Middle name must be a string.',
      'string.min': 'Middle name must be at least 0 characters long.',
      'string.max': 'Middle name must not exceed 50 characters.',
    }),

    last_name: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'Last name must be a string.',
      'string.min': 'Last name must be at least 2 characters long.',
      'string.max': 'Last name must not exceed 50 characters.',
    }),

    gender: Joi.string()
      .lowercase()
      .valid(...GENDER_ARRAY)
      .optional()
      .messages({
        'string.base': 'Gender must be a string.',
        'any.only': `Gender must be one of: ${GENDER_ARRAY.join(', ')}.`,
      }),

    date_of_birth: Joi.date().iso().optional().messages({
      'date.base': 'Date of birth must be a valid date.',
      'date.format': 'Date of birth must be in ISO format (YYYY-MM-DD).',
    }),

    location: Joi.string().trim().lowercase().min(2).max(100).optional().messages({
      'string.base': 'Location must be a string.',
      'string.min': 'Location must be at least 2 characters long.',
      'string.max': 'Location must not exceed 100 characters.',
    }),

    home_address: Joi.string().trim().lowercase().min(2).max(200).optional().messages({
      'string.base': 'Home address must be a string.',
      'string.min': 'Home address must be at least 2 characters long.',
      'string.max': 'Home address must not exceed 200 characters.',
    }),

    email: Joi.string().trim().lowercase().email().min(5).max(100).optional().messages({
      'string.email': 'Email must be a valid email address.',
      'string.min': 'Email must be at least 5 characters long.',
      'string.max': 'Email must not exceed 100 characters.',
    }),

    phone_number: Joi.string().trim().min(5).max(15).optional().messages({
      'string.base': 'Phone number must be a string.',
      'string.min': 'Phone number must be at least 5 characters long.',
      'string.max': 'Phone number must not exceed 15 characters.',
    }),

    emergency_number: Joi.string().trim().min(5).max(15).allow('').optional().messages({
      'string.base': 'Emergency number must be a string.',
      'string.min': 'Emergency number must be at least 5 characters long.',
      'string.max': 'Emergency number must not exceed 15 characters.',
    }),

    national_id: Joi.string().trim().lowercase().min(5).max(20).allow('').optional().messages({
      'string.base': 'National ID must be a string.',
      'string.min': 'National ID must be at least 5 characters long.',
      'string.max': 'National ID must not exceed 20 characters.',
    }),

    employment_type: Joi.string()
      .lowercase()
      .valid(...EMPLOYMENT_TYPE_ARRAY)
      .optional()
      .messages({
        'string.base': 'Employment type must be a string.',
        'any.only': `Employment type must be one of: ${EMPLOYMENT_TYPE_ARRAY.join(', ')}.`,
      }),

    employee_id: Joi.string().trim().lowercase().min(2).max(20).allow('').optional().messages({
      'string.base': 'Employee ID must be a string.',
      'string.min': 'Employee ID must be at least 2 characters long.',
      'string.max': 'Employee ID must not exceed 20 characters.',
    }),

    employment_start_date: Joi.date().iso().optional().messages({
      'date.base': 'Employment start date must be a valid date.',
      'date.format': 'Employment start date must be in ISO format (YYYY-MM-DD).',
    }),

    employment_end_date: Joi.date().iso().optional().default(null).messages({
      'date.base': 'Employment end date must be a valid date.',
      'date.format': 'Employment end date must be in ISO format (YYYY-MM-DD).',
    }),

    role: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'Role must be a string.',
      'string.min': 'Role must be at least 2 characters long.',
      'string.max': 'Role must not exceed 50 characters.',
    }),

    kind_of_payment: Joi.string()
      .lowercase()
      .valid(...KIND_OF_PAYMENT_ARRAY)
      .optional()
      .messages({
        'string.base': 'Kind of payment must be a string.',
        'any.only': `Kind of payment must be one of: ${KIND_OF_PAYMENT_ARRAY.join(', ')}.`,
      }),

    mode_of_payment: Joi.string()
      .lowercase()
      .valid(...MODE_OF_PAYMENT_ARRAY)
      .optional()
      .messages({
        'string.base': 'Mode of payment must be a string.',
        'any.only': `Mode of payment must be one of: ${MODE_OF_PAYMENT_ARRAY.join(', ')}.`,
      }),

    salary: Joi.number().precision(2).min(0).default(0).optional().messages({
      'number.base': 'Salary must be a number.',
      'number.min': 'Salary must be at least 0.01.',
    }),

    hourly_rate: Joi.number().precision(2).min(0).default(0).optional().messages({
      'number.base': 'Hourly rate must be a number.',
      'number.min': 'Hourly rate must be at least 0.01.',
    }),

    work_hours_per_week: Joi.number().integer().min(1).default(0).optional().messages({
      'number.base': 'Work hours per week must be a number.',
      'number.min': 'Work hours per week must be at least 1.',
    }),

    bonus_percent: Joi.number().integer().min(0).max(100).default(0).optional().messages({
      'number.base': 'Bonus percentage must be a number.',
      'number.min': 'Bonus percentage cannot be negative.',
      'number.max': 'Bonus percentage cannot exceed 100.',
    }),

    bonus_interval: Joi.string()
      .lowercase()
      .valid(...BONUS_INTERVAL_ARRAY)
      .default(BONUS_INTERVAL_ENUM.NULL)
      .optional()
      .messages({
        'string.base': 'Bonus interval must be a string.',
        'any.only': `Bonus interval must be one of: ${BONUS_INTERVAL_ARRAY.join(', ')}.`,
      }),

    bank_name: Joi.string().trim().lowercase().min(2).max(50).allow('').optional().messages({
      'string.base': 'Bank name must be a string.',
      'string.min': 'Bank name must be at least 2 characters long.',
      'string.max': 'Bank name must not exceed 50 characters.',
    }),

    bank_account_name: Joi.string()
      .trim()
      .lowercase()
      .min(2)
      .max(50)
      .allow('')
      .optional()
      .messages({
        'string.base': 'Bank account name must be a string.',
        'string.min': 'Bank account name must be at least 2 characters long.',
        'string.max': 'Bank account name must not exceed 50 characters.',
      }),

    bank_account_number: Joi.string().trim().min(5).max(20).allow('').optional().messages({
      'string.base': 'Bank account number must be a string.',
      'string.min': 'Bank account number must be at least 5 characters long.',
      'string.max': 'Bank account number must not exceed 20 characters.',
    }),

    employment_status: Joi.string()
      .lowercase()
      .valid(...EMPLOYMENT_STATUS_ARRAY)
      .default(EMPLOYMENT_STATUS_ENUM.ACTIVE)
      .optional()
      .messages({
        'string.base': 'Employment status must be a string.',
        'any.only': `Employment status must be one of: ${EMPLOYMENT_STATUS_ARRAY.join(', ')}.`,
      }),

    tax_code: Joi.string().trim().lowercase().min(2).max(20).allow('').optional().messages({
      'string.base': 'Tax code must be a string.',
      'string.min': 'Tax code must be at least 2 characters long.',
      'string.max': 'Tax code must not exceed 20 characters.',
    }),

    tax_rate: Joi.number().precision(2).min(0).default(0).optional().messages({
      'number.base': 'Tax rate must be a number.',
    }),

    tax_number: Joi.string().trim().min(5).max(20).allow('').optional().messages({
      'string.base': 'Tax number must be a string.',
      'string.min': 'Tax number must be at least 5 characters long.',
      'string.max': 'Tax number must not exceed 20 characters.',
    }),
  })
    .optional()
    .custom(createEmployeeCustomValidator, 'Create Employee Custom Validator')
    .messages({
      'date.employmentEndDateIsRequired':
        'Employment end date is required for contract employment.',
      'string.bankDetailsAreRequired':
        'Bank details (bank name, account name, and account number) are required for electronic payment.',
      'sting.bankDetailsMustBeComplete':
        'All bank details (bank name, account name, and account number) must be provided together.',
      'string.hourlyRateAndWorkHoursAreRequired':
        'Hourly rate and work hours per week are required for hourly payment.',
      'string.eitherSalaryOrHourlyRateMustBeProvided':
        'Either salary or hourly rate with work hours can be provided not both.',
      'string.salaryMustBeProvided': 'Salary must be provided if kind of payment is salary',
    }),

  deductibles: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().uuid().messages({
          'string.guid': 'ID must be a valid UUID.',
        }),

        reason: Joi.string().trim().lowercase().min(2).max(100).required().messages({
          'any.required': 'Reason is required.',
          'string.base': 'Reason must be a string.',
          'string.min': 'Reason must be at least 2 characters long.',
          'string.max': 'Reason must not exceed 100 characters.',
        }),

        value: Joi.number().precision(2).positive().required().messages({
          'any.required': 'Value is required.',
          'number.base': 'Value must be a valid number.',
          'number.positive': 'Value must be greater than zero.',
          'number.precision': 'Value must have at most 2 decimal places.',
        }),

        start_date: Joi.date().iso().optional().messages({
          'date.base': 'Start date must be a valid ISO date.',
        }),

        end_date: Joi.date().iso().optional().messages({
          'date.base': 'End date must be a valid ISO date.',
        }),

        one_time: Joi.boolean().optional().messages({
          'boolean.base': 'One-time must be a boolean value.',
        }),
      })
        .custom(createDeductibleCustomValidator, 'Create Deductible Custom Validator')
        .messages({
          'string.ifOneTimeStartDateEqualsEndDate':
            'If one_time is true, start_date and end_date must be the same.',
          'date.endDateMustBeGreaterOrEqualToStartDate':
            'End date must be equal to or greater than start date.',
        })
    )
    .optional(),

  pension: Joi.object({
    provider: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'Provider must be a string.',
      'string.min': 'Provider must be at least 2 characters long.',
      'string.max': 'Provider must not exceed 50 characters.',
    }),
    policy_number: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'Policy number must be a string.',
      'string.min': 'Policy number must be at least 2 characters long.',
      'string.max': 'Policy number must not exceed 50 characters.',
    }),
    start_date: Joi.date().iso().optional().messages({
      'date.base': 'Start date must be a valid ISO date.',
    }),
    monthly_contribution: Joi.number().precision(2).optional().messages({
      'number.base': 'Monthly contribution must be a number.',
    }),
    beneficiary_first_name: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'Beneficiary first name must be a string.',
      'string.min': 'Beneficiary first name must be at least 2 characters long.',
      'string.max': 'Beneficiary first name must not exceed 50 characters.',
    }),
    beneficiary_middle_name: Joi.string()
      .trim()
      .lowercase()
      .min(0)
      .max(50)
      .allow('')
      .optional()
      .messages({
        'string.base': 'Beneficiary middle name must be a string.',
        'string.min': 'Beneficiary middle name must be at least 0 characters long.',
        'string.max': 'Beneficiary middle name must not exceed 50 characters.',
      }),
    beneficiary_last_name: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'Beneficiary last name must be a string.',
      'string.min': 'Beneficiary last name must be at least 2 characters long.',
      'string.max': 'Beneficiary last name must not exceed 50 characters.',
    }),
    beneficiary_phone_number: Joi.string().trim().min(5).max(15).optional().messages({
      'string.base': 'Beneficiary phone number must be a string.',
      'string.min': 'Beneficiary phone number must be at least 5 characters long.',
      'string.max': 'Beneficiary phone number must not exceed 15 characters.',
    }),
    beneficiary_relation: Joi.string().trim().lowercase().min(2).max(50).optional().messages({
      'string.base': 'Beneficiary relation must be a string.',
      'string.min': 'Beneficiary relation must be at least 2 characters long.',
      'string.max': 'Beneficiary relation must not exceed 50 characters.',
    }),
    beneficiary_date_of_birth: Joi.date().iso().optional().messages({
      'date.base': 'Beneficiary date of birth must be a valid ISO date.',
    }),
  }).optional(),
})
  .custom(editEmployeeCustomValidator)
  .messages({
    'any.required': 'At least one of employee_details, pension, or deductibles must be provided.',
  });

export const createLeaveSchema = Joi.object<ILeaveAttributes>({
  type: Joi.string()
    .valid(...LEAVE_TYPE_ARRAY)
    .required()
    .trim()
    .lowercase()
    .messages({
      'string.base': 'Leave type must be a string.',
      'any.only': `Leave type must be one of: ${LEAVE_TYPE_ARRAY.join(', ')}`,
      'any.required': 'Leave type is required.',
    }),

  start_date: Joi.date().iso().required().messages({
    'date.base': 'Start date must be a valid date.',
    'date.format': 'Start date must be in ISO 8601 format (YYYY-MM-DD).',
    'any.required': 'Start date is required.',
  }),

  end_date: Joi.date().iso().greater(Joi.ref('start_date')).required().messages({
    'date.base': 'End date must be a valid date.',
    'date.format': 'End date must be in ISO 8601 format (YYYY-MM-DD).',
    'date.greater': 'End date must be after the start date.',
    'any.required': 'End date is required.',
  }),
})
  .custom(createLeaveCustomValidator, 'Create Leave Custom Validator')
  .messages({
    'date.endDateMustBeGreaterThanStartDate':
      'End date must be a date greater than the start date.',
    'date.cannotSetCompleteStatusForFutureEndDate':
      'Leave status cannot be set as complete for future leave end date.',
  });
