import OrganizationAPIs from '../api/endpoints/organization.apis';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class OrganizationServices extends ErrorWrapper {
  constructor(private organizationApis: OrganizationAPIs) {
    super();
  }

  async getAllOrganizations(offset = 1, limit = 50) {
    return await this.organizationApis.getAllOrganizations(offset, limit);
  }

  async getOneOrganization(organizationId: string) {
    return await this.organizationApis.getOneOrganization(organizationId);
  }
}
