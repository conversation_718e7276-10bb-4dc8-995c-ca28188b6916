import UserAPIs from '../api/endpoints/user.apis';
import { ErrorWrapper } from '../helpers/class.helpers';

export default class UserServices extends ErrorWrapper {
  constructor(private userApis: UserAPIs) {
    super();
  }
  async getAllUsers(offset = 1, limit = 50) {
    return await this.userApis.getAllUsers(offset, limit);
  }

  async getOneUser(userId: string) {
    return await this.userApis.getOneUser(userId);
  }

  async getUserStats(filter: string) {
    return await this.userApis.getUserStats(filter);
  }
}
