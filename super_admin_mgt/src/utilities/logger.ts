import winston from 'winston';
import { isProductionEnv } from './guards';
const { timestamp, combine, printf } = winston.format;

const loggerFormat = printf(({ timestamp, level, name, message, ...metadata }) => {
  const logMessage = {
    level,
    name,
    message,
    timestamp,
    ...metadata,
    stack: metadata?.stack,
  };
  return JSON.stringify(logMessage, null, 2);
});

const infoFilter = winston.format((info) => {
  return info.level === 'info' ? info : false;
});

const getLoggerTransport = (infoFilename: string, errorFilename: string) => {
  const transports: winston.transport[] = [];

  if (!isProductionEnv) {
    transports.push(new winston.transports.Console());
  }
  transports.push(
    new winston.transports.File({
      format: winston.format.combine(infoFilter()),
      filename: infoFilename,
      level: 'info',
    })
  );

  transports.push(
    new winston.transports.File({
      filename: errorFilename,
      level: 'error',
    })
  );

  return transports;
};

const logger = winston.createLogger({
  format: combine(timestamp(), loggerFormat),
  transports: getLoggerTransport('logs/app/info.log', 'logs/app/errors.log'),
});

export const axiosLogger = winston.createLogger({
  format: combine(timestamp(), loggerFormat),
  transports: getLoggerTransport('logs/axios/info.log', 'logs/axios/errors.log'),
});

export default logger;
