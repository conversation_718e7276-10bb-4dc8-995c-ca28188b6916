import { Request, Response, NextFunction, RequestHandler } from 'express';

export const catchAsync = (fn: RequestHandler) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((err) => {
      return next(err);
    });
  };
};

export const catchError = <F extends (...args: any[]) => any>(fn: F): F => {
  return ((...args: Parameters<F>) => {
    try {
      const result = fn(...args);

      if (result instanceof Promise) {
        return result.catch((error) => {
          let err = error;

          if (!(err instanceof Error)) {
            err = new Error(String(error));
          }

          throw err;
        });
      }

      return result;
    } catch (error) {
      let err = error;

      if (!(err instanceof Error)) {
        err = new Error(String(error));
      }

      throw err;
    }
  }) as F;
};
