import { Request } from 'express';
import RequestIP from 'request-ip';
import { v4 as uuidv4 } from 'uuid';
import { BASE_10, FILE_EXTENSION } from '../constants/values.constants';
import geoip from 'geoip-lite';
import { toZonedTime } from 'date-fns-tz';
import { isDevelopmentEnv, isProductionEnv } from './guards';

export const getUserAgentHeader = (req: Request) => req.headers['user-agent'];

export const generateToken = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

export const genReference = () => {
  const reference = uuidv4().slice(0, 12).split('-').join('');
  return reference;
};

export const formatDateToYearMonthDay = (dateValue: Date) => {
  const date = new Date(dateValue);
  const options: any = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  };
  const formattedDate = date.toLocaleString('en-GB', options).replace(',', '');
  if (formattedDate === 'Invalid Date') return null;
  return formattedDate;
};

export const pagination = (req: Request): { offset: number; limit: number } => {
  const { offset = 1, limit = 50 } = req.query;

  let parsedPerPage: number = typeof limit === 'string' ? parseInt(limit) : (limit as number);
  if (isNaN(parsedPerPage)) {
    parsedPerPage = 50;
  }

  let parsedPage: number = typeof offset === 'string' ? parseInt(offset) : (offset as number);
  if (isNaN(parsedPage)) {
    parsedPage = 1;
  }
  const paginate = {
    offset: (parsedPage - 1) * parsedPerPage,
    limit: parsedPerPage,
  };
  return paginate;
};

export const getPagination = (req: Request): { page: number; offset: number; limit: number } => {
  const { page = 1, limit = 50 } = req.query;

  let parsedLimit: number =
    typeof limit === 'string' ? parseInt(limit, BASE_10) : (limit as number);
  if (isNaN(parsedLimit)) {
    parsedLimit = 50;
  }

  let parsedPage: number = typeof page === 'string' ? parseInt(page, BASE_10) : (page as number);
  if (isNaN(parsedPage)) {
    parsedPage = 1;
  }

  const paginate = {
    page: parsedPage,
    offset: (parsedPage - 1) * parsedLimit,
    limit: parsedLimit,
  };

  return paginate;
};

export const getFileName = (filetype: string, filename: string): string | undefined => {
  if (filetype === 'pdf') return filename + '.' + FILE_EXTENSION.PDF;
};

export const removeWhiteSpace = (word: string): string | undefined => {
  if (!word) return;
  return word.replace(/\s+/g, '');
};

export const trimAndLowerCase = (word: string): string => {
  if (!word) return '';
  return word.trim().toLowerCase();
};

export const getPublicAddress = (req: Request): string => {
  const xForwardedFor = req.header('x-forwarded-for');
  const ip =
    (xForwardedFor ? xForwardedFor.split(',')[0].trim() : null) ||
    RequestIP.getClientIp(req) ||
    req.ips[0] ||
    req.ip;
  if (!ip) return '';
  if (ip === '::1' || ip === '127.0.0.1' || ip.startsWith('::ffff:')) {
    return 'localhost';
  }
  return ip;
};

export const getUserTimeZone = (req: Request): string => {
  const ip = getPublicAddress(req) || '0.0.0.0';
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'UTC';
  return userTimezone;
};

export const formatDateToYearMonthDayTime = (dateValue: Date, userTimezone: string) => {
  if (!dateValue) return null;
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true, // Enables AM/PM format
    timeZone: userTimezone, // Ensures consistent time formatting
  };

  const formattedDate = new Date(dateValue).toLocaleString('en-GB', options).replace(',', '');
  if (formattedDate === 'Invalid Date') return null;
  return formattedDate;
};

export const convertHexToRgba = (hex: string, opacity: number) => {
  if (hex && opacity) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
};

export const currentTimestamp = (req: Request) => {
  const ip = req.ip || '0.0.0.0'; // Replace with a default IP if needed
  const geo = geoip.lookup(ip);
  const userTimezone = geo?.timezone || 'UTC';
  const utcDate = new Date(); // Current time in UTC
  const zonedDate = toZonedTime(utcDate, userTimezone);
  return zonedDate;
};

export const WEB_EVENTS_URL =
  isProductionEnv || isDevelopmentEnv
    ? process.env.WEB_EVENT_URL
    : 'http://127.0.0.1:4000/webevents/notification';
export const BASE_URL = isProductionEnv ? process.env.SERVER_PROD_URL : process.env.SERVER_DEV_URL;

export const LOCALHOST = `http:127.0.0.1:${process.env.PORT || 8752}`;
export const GOOGLE_API_BASE_URL = 'https://www.googleapis.com';
export const GOOGLE_ACCOUNT_BASE_URL = 'https://accounts.google.com/o/oauth2/v2/auth';

export const getUpdatedFields = <T extends object>(
  existingData: T,
  updatedData: Partial<T>
): Partial<T> =>
  Object.fromEntries(
    Object.entries(updatedData).filter(([key, value]) => existingData[key as keyof T] !== value)
  ) as Partial<T>;

import jwt from 'jsonwebtoken';
import { IMicroServiceInternalAuthPayload } from '../interfaces/global.interfaces';

export function generateInternalAuthToken(payload: IMicroServiceInternalAuthPayload) {
  const INTERNAL_SECRET = process.env.INTERNAL_JWT_SECRET as string;

  return jwt.sign(payload, INTERNAL_SECRET, { expiresIn: 60 });
}
