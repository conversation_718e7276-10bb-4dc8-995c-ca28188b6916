interface UserDetails {
  id?: string;
  orgId?: string;
  email?: string;
  anonymous?: boolean;
}

interface RequestDetails {
  ipAddress: string;
  userAgent: string;
  browser?: string;
  os?: string;
  method: string;
  url: string;
  body?: any;
  createdAt: Date;
  hostname: string;
  timezone: string;
}

interface ServerDetails {
  ipAddress: string;
  name: string;
  platform: string;
  memory: number;
  cpuCount: number;
  server_time: Date;
}

interface ResponseDetails {
  statusCode: number;
  message?: string;
  data?: any;
}

export interface RequestLogDetails {
  serverDetails: ServerDetails;
  requestDetails: RequestDetails;
  userDetails: UserDetails;
}

export interface LogDetails {
  userId?: string;
  orgId?: string;
  anonymous?: boolean;
  action: string;
  details: {
    userDetails: UserDetails;
    requestDetails: RequestDetails;
    serverDetails: ServerDetails;
    responseDetails: ResponseDetails;
    [key: string]: unknown;
  };
}
